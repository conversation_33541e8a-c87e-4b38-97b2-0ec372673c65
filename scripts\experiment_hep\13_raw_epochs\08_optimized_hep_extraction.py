#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化的HEP提取脚本
基于诊断结果改进信号处理方法，增强HEP信号可见性

主要改进：
1. 使用更宽的滤波范围（0.1-45Hz）
2. 优化基线校正方法
3. 增强信号可见性
4. 使用最佳ECG通道

作者: HEP分析团队
日期: 2024年
"""

import os
import sys
import numpy as np
import mne
import neurokit2 as nk
import logging
import warnings
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 忽略警告
warnings.filterwarnings('ignore')
mne.set_log_level('ERROR')

# 基础路径配置
BASE_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
DATA_DIR = r"D:\ecgeeg\19-eegecg手动预处理6-ICA3"
OUTPUT_DIR = os.path.join(BASE_DIR, 'result', 'hep_analysis', '13_raw_epochs', '08_optimized_extraction')
os.makedirs(OUTPUT_DIR, exist_ok=True)

# 数据结构配置
TOTAL_CHANNELS = 121  # 实际通道数
EEG_CHANNELS = 61
SAMPLING_RATE = 500

# 优化的HEP提取参数
HEP_TMIN = -0.5
HEP_TMAX = 1.5
DISPLAY_TMIN = -0.2
DISPLAY_TMAX = 0.65

# 优化的基线校正：使用更短的窗口，避免心脏前期活动
BASELINE_TMIN = -0.1
BASELINE_TMAX = -0.05

# 优化的滤波参数：保留更多低频成分
FILTER_LOW = 0.1    # 降低低频截止，保留更多HEP成分
FILTER_HIGH = 45.0  # 提高高频截止，保留更多细节

# 最佳ECG通道（基于诊断结果）
BEST_ECG_CHANNELS = ['ECG7', 'ECG11', 'ECG8', 'ECG12']

# 电极组合
CENTRAL_ELECTRODES = ['F1', 'F2', 'FC1', 'FC2', 'C1', 'Cz', 'C2', 'Fz']

def setup_logging():
    """设置日志系统"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler(sys.stdout)]
    )
    return logging.getLogger(__name__)

def load_data(subject_id=1, stage_number='01', stage_type='prac', logger=None):
    """加载数据文件"""
    try:
        filename = f"{subject_id:02d}_{stage_number}_reto2_combined_baseline_corrected_chanloc_bad_interp_avgref_ica_{stage_type}.fif"
        file_path = os.path.join(DATA_DIR, filename)
        
        if not os.path.exists(file_path):
            logger.error(f"文件不存在: {filename}")
            return None, None
        
        raw = mne.io.read_raw_fif(file_path, preload=True, verbose=False)
        logger.info(f"成功加载数据: {filename}")
        logger.info(f"数据形状: {raw.get_data().shape}")
        
        return raw, filename
        
    except Exception as e:
        logger.error(f"加载数据失败: {str(e)}")
        return None, None

def convert_to_microvolts(raw, logger):
    """转换数据到微伏单位"""
    try:
        # 基于诊断结果，数据是mV单位，需要转换为μV
        conversion_factor = 1000.0  # mV to μV
        
        # 转换所有通道
        raw._data *= conversion_factor
        
        logger.info(f"数据已转换为μV单位，转换系数: {conversion_factor}")
        
        # 验证转换后的数据
        eeg_data = raw.get_data()[:EEG_CHANNELS, :]
        eeg_std = np.std(eeg_data)
        logger.info(f"转换后EEG标准差: {eeg_std:.2f}μV")
        
        return raw
        
    except Exception as e:
        logger.error(f"电压单位转换失败: {str(e)}")
        return raw

def apply_optimized_filtering(raw, logger):
    """应用优化滤波"""
    try:
        raw_filtered = raw.copy()
        
        # 应用优化的带通滤波
        raw_filtered.filter(l_freq=FILTER_LOW, h_freq=FILTER_HIGH, fir_design='firwin', verbose=False)
        
        logger.info(f"应用优化滤波: {FILTER_LOW}-{FILTER_HIGH}Hz")
        
        # 验证滤波后的信号质量
        eeg_data = raw_filtered.get_data()[:EEG_CHANNELS, :]
        eeg_std = np.std(eeg_data)
        logger.info(f"滤波后EEG标准差: {eeg_std:.2f}μV")
        
        return raw_filtered
        
    except Exception as e:
        logger.error(f"滤波失败: {str(e)}")
        return raw

def find_best_ecg_channel(raw, logger):
    """找到最佳的ECG通道"""
    try:
        ch_names = raw.ch_names
        data = raw.get_data()
        
        best_channel = None
        best_std = 0
        
        for ch_name in BEST_ECG_CHANNELS:
            if ch_name in ch_names:
                ch_idx = ch_names.index(ch_name)
                ch_data = data[ch_idx, :]
                ch_std = np.std(ch_data)
                
                logger.info(f"{ch_name} 标准差: {ch_std:.2f}μV")
                
                if ch_std > best_std:
                    best_std = ch_std
                    best_channel = ch_name
        
        if best_channel:
            logger.info(f"选择最佳ECG通道: {best_channel} (标准差: {best_std:.2f}μV)")
            return best_channel
        else:
            logger.warning("未找到合适的ECG通道，使用ECG11")
            return 'ECG11'
            
    except Exception as e:
        logger.error(f"ECG通道选择失败: {str(e)}")
        return 'ECG11'

def extract_ecg_and_detect_r_peaks(raw, ecg_channel, logger):
    """提取心电信号并检测R波"""
    try:
        ch_names = raw.ch_names
        
        if ecg_channel not in ch_names:
            logger.error(f"ECG通道 {ecg_channel} 不存在")
            return None, None
        
        # 提取ECG通道
        ecg_idx = ch_names.index(ecg_channel)
        ecg_signal = raw.get_data()[ecg_idx, :]
        
        logger.info(f"提取心电通道: {ecg_channel}")
        logger.info(f"心电信号统计: 标准差={np.std(ecg_signal):.2f}μV, 峰峰值={np.max(ecg_signal)-np.min(ecg_signal):.2f}μV")
        
        # 清洗ECG信号
        ecg_cleaned = nk.ecg_clean(ecg_signal, sampling_rate=SAMPLING_RATE)
        
        # 使用多种方法检测R峰，选择最佳结果
        methods = ['neurokit', 'pantompkins1985', 'hamilton2002']
        best_peaks = None
        best_count = 0
        
        for method in methods:
            try:
                _, rpeaks_info = nk.ecg_peaks(ecg_cleaned, sampling_rate=SAMPLING_RATE, method=method)
                r_peaks = rpeaks_info.get('ECG_R_Peaks', [])
                
                if len(r_peaks) > best_count:
                    best_count = len(r_peaks)
                    best_peaks = r_peaks
                    
                logger.info(f"方法 {method}: 检测到 {len(r_peaks)} 个R峰")
                
            except Exception as e:
                logger.warning(f"方法 {method} 失败: {str(e)}")
                continue
        
        if best_peaks is None or len(best_peaks) < 30:
            logger.error(f"R峰检测失败或数量过少: {len(best_peaks) if best_peaks is not None else 0}")
            return None, None
        
        logger.info(f"最终选择: {len(best_peaks)} 个R峰")
        return best_peaks, ecg_cleaned
        
    except Exception as e:
        logger.error(f"R峰检测失败: {str(e)}")
        return None, None

def extract_hep_epochs_optimized(raw, r_peaks, logger):
    """提取优化的HEP epochs"""
    try:
        # 创建事件数组
        events = np.column_stack([r_peaks, np.zeros_like(r_peaks), np.ones_like(r_peaks)])
        
        # 创建epochs
        epochs = mne.Epochs(raw, events, event_id=1,
                           tmin=HEP_TMIN, tmax=HEP_TMAX,
                           baseline=None, preload=True, reject=None, verbose=False)
        
        # 优化的基线校正
        baseline_mask = (epochs.times >= BASELINE_TMIN) & (epochs.times <= BASELINE_TMAX)
        data = epochs.get_data()
        
        # 计算基线平均值
        baseline_data = data[:, :, baseline_mask]
        baseline_mean = np.mean(baseline_data, axis=2, keepdims=True)
        
        # 应用基线校正
        data_corrected = data - baseline_mean
        epochs._data = data_corrected
        
        logger.info(f"创建了 {len(epochs)} 个HEP epochs")
        logger.info(f"优化基线校正窗口: {BASELINE_TMIN*1000:.0f}ms 到 {BASELINE_TMAX*1000:.0f}ms")
        
        # 验证基线校正效果
        baseline_after = np.mean(data_corrected[:, :EEG_CHANNELS, baseline_mask])
        logger.info(f"基线校正后平均值: {baseline_after:.6f} μV")
        
        # 计算HEP信号统计
        eeg_data = data_corrected[:, :EEG_CHANNELS, :]
        hep_std = np.std(eeg_data)
        hep_range = np.max(eeg_data) - np.min(eeg_data)
        logger.info(f"HEP信号统计: 标准差={hep_std:.3f}μV, 动态范围={hep_range:.3f}μV")
        
        return epochs
        
    except Exception as e:
        logger.error(f"HEP epochs提取失败: {str(e)}")
        return None

def create_enhanced_visualization(epochs, subject_id, stage_type, stage_number, ecg_channel, logger):
    """创建增强的HEP可视化"""
    try:
        # 获取EEG数据
        eeg_data = epochs.get_data()[:, :EEG_CHANNELS, :]
        mean_hep = np.mean(eeg_data, axis=0)
        
        # 找到中央电极索引
        central_indices = []
        for electrode in CENTRAL_ELECTRODES:
            if electrode in epochs.ch_names[:EEG_CHANNELS]:
                central_indices.append(epochs.ch_names.index(electrode))
        
        if not central_indices:
            logger.warning("未找到中央电极")
            return None
        
        # 计算中央电极平均波形
        central_data = mean_hep[central_indices, :]
        central_mean = np.mean(central_data, axis=0)
        central_std = np.std(central_data, axis=0)
        
        # 创建可视化窗口
        display_mask = (epochs.times >= DISPLAY_TMIN) & (epochs.times <= DISPLAY_TMAX)
        display_times = epochs.times[display_mask] * 1000  # 转换为ms
        display_signal = central_mean[display_mask]
        display_std = central_std[display_mask]
        
        # 创建图表
        fig, ax = plt.subplots(figsize=(14, 8))
        
        # 绘制平均波形和标准差
        ax.plot(display_times, display_signal, 'b-', linewidth=3, 
               label=f'中央电极平均 (n={len(central_indices)})')
        ax.fill_between(display_times, display_signal - display_std, display_signal + display_std, 
                       alpha=0.3, color='blue')
        
        # 标记重要时间点和区域
        ax.axvline(x=0, color='red', linestyle='--', linewidth=2, alpha=0.8, label='R波')
        ax.axvspan(200, 600, alpha=0.15, color='yellow', label='HEP窗口 (200-600ms)')
        ax.axvspan(BASELINE_TMIN*1000, BASELINE_TMAX*1000, alpha=0.15, color='green', 
                  label=f'基线校正窗口 ({BASELINE_TMIN*1000:.0f}到{BASELINE_TMAX*1000:.0f}ms)')
        
        # 设置坐标轴
        ax.set_xlabel('时间 (ms)', fontsize=12)
        ax.set_ylabel('幅度 (μV)', fontsize=12)
        ax.set_title(f'被试{subject_id:02d} - {stage_type}_{stage_number} 优化HEP波形\n'
                    f'滤波: {FILTER_LOW}-{FILTER_HIGH}Hz, ECG通道: {ecg_channel}, Epochs: {len(epochs)}', 
                    fontsize=14, fontweight='bold')
        ax.grid(True, alpha=0.3)
        ax.legend(fontsize=10)
        
        # 优化Y轴范围以突出HEP特征
        signal_range = np.max(np.abs(display_signal))
        if signal_range > 0:
            y_margin = signal_range * 0.2
            ax.set_ylim(-signal_range - y_margin, signal_range + y_margin)
        
        # 设置2:1的宽高比
        ax.set_aspect('auto')
        
        plt.tight_layout()
        
        # 保存图表
        output_filename = f"subject_{subject_id:02d}_{stage_type}_{stage_number}_optimized_hep.png"
        output_path = os.path.join(OUTPUT_DIR, output_filename)
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"优化HEP可视化已保存: {output_path}")
        
        # 输出详细的信号统计信息
        logger.info(f"优化HEP信号统计:")
        logger.info(f"  - 信号幅度范围: {np.min(display_signal):.3f} 到 {np.max(display_signal):.3f} μV")
        logger.info(f"  - 信号标准差: {np.std(display_signal):.3f} μV")
        logger.info(f"  - 峰峰值: {np.max(display_signal) - np.min(display_signal):.3f} μV")
        logger.info(f"  - 信噪比估计: {signal_range / np.std(display_std) if np.std(display_std) > 0 else 'N/A':.2f}")
        
        return output_path
        
    except Exception as e:
        logger.error(f"创建优化HEP可视化失败: {str(e)}")
        return None

def main():
    """主函数"""
    logger = setup_logging()
    logger.info("开始优化HEP提取测试")
    
    # 测试参数
    subject_id = 1
    stage_number = '01'
    stage_type = 'prac'
    
    logger.info(f"测试被试: {subject_id:02d}")
    logger.info(f"阶段: {stage_type}_{stage_number}")
    logger.info(f"优化参数: 滤波 {FILTER_LOW}-{FILTER_HIGH}Hz, 基线 {BASELINE_TMIN*1000:.0f}到{BASELINE_TMAX*1000:.0f}ms")
    
    # 1. 加载数据
    raw, filename = load_data(subject_id, stage_number, stage_type, logger)
    if raw is None:
        logger.error("数据加载失败，退出")
        return
    
    # 2. 转换电压单位
    raw = convert_to_microvolts(raw, logger)
    
    # 3. 应用优化滤波
    raw_filtered = apply_optimized_filtering(raw, logger)
    
    # 4. 选择最佳ECG通道
    best_ecg_channel = find_best_ecg_channel(raw_filtered, logger)
    
    # 5. 提取心电信号并检测R波
    r_peaks, ecg_cleaned = extract_ecg_and_detect_r_peaks(raw_filtered, best_ecg_channel, logger)
    if r_peaks is None:
        logger.error("R波检测失败，退出")
        return
    
    # 6. 提取优化的HEP epochs
    epochs = extract_hep_epochs_optimized(raw_filtered, r_peaks, logger)
    if epochs is None:
        logger.error("HEP epochs提取失败，退出")
        return
    
    # 7. 创建增强可视化
    viz_path = create_enhanced_visualization(epochs, subject_id, stage_type, stage_number, best_ecg_channel, logger)
    
    logger.info("=== 优化HEP提取测试完成 ===")
    logger.info(f"成功提取 {len(epochs)} 个HEP epochs")
    logger.info(f"检测到 {len(r_peaks)} 个R峰")
    logger.info(f"使用ECG通道: {best_ecg_channel}")
    if viz_path:
        logger.info(f"可视化文件: {viz_path}")

if __name__ == "__main__":
    main()
