#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
HEP数据提取脚本测试版本 - 仅处理少量数据进行验证

测试配置：
- 仅处理被试01和02
- 仅处理prac_01阶段
- 验证所有功能模块是否正常工作
"""

import sys
import os
import importlib.util

# 添加脚本路径
script_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(script_dir)

# 动态导入主脚本
main_script_path = os.path.join(script_dir, "03_comprehensive_hep_extraction.py")
spec = importlib.util.spec_from_file_location("comprehensive_hep_extraction", main_script_path)
comprehensive_hep = importlib.util.module_from_spec(spec)
spec.loader.exec_module(comprehensive_hep)

# 导入需要的函数和变量
setup_logging = comprehensive_hep.setup_logging
process_single_subject_comprehensive = comprehensive_hep.process_single_subject_comprehensive
save_stage_data_to_hdf5_comprehensive = comprehensive_hep.save_stage_data_to_hdf5_comprehensive
ProgressMonitorComprehensive = comprehensive_hep.ProgressMonitorComprehensive
DATA_DIR = comprehensive_hep.DATA_DIR
OUTPUT_DIR = comprehensive_hep.OUTPUT_DIR
PLOTS_DIR = comprehensive_hep.PLOTS_DIR
LOGS_DIR = comprehensive_hep.LOGS_DIR

import h5py

def test_main():
    """测试版主函数"""
    # 设置日志
    logger = setup_logging()

    logger.info("=== HEP数据提取脚本 - 测试版本 ===")
    logger.info("仅处理被试01和02的prac_01阶段进行功能验证")

    # 测试配置
    test_subjects = [1, 2]  # 仅测试前两个被试
    test_stage_config = {'prac': ['01']}  # 仅测试prac_01

    logger.info(f"数据源目录: {DATA_DIR}")
    logger.info(f"输出目录: {OUTPUT_DIR}")
    logger.info(f"测试被试: {test_subjects}")
    logger.info(f"测试阶段: {test_stage_config}")

    # 计算测试文件数
    total_files = len(test_subjects) * len(test_stage_config['prac'])
    logger.info(f"预计处理文件总数: {total_files}")

    progress_monitor = ProgressMonitorComprehensive(total_files, logger)

    # 按阶段处理数据
    for stage_type, stage_numbers in test_stage_config.items():
        for stage_number in stage_numbers:
            logger.info(f"\n开始处理测试阶段: {stage_type}_{stage_number}")

            stage_results = []

            # 处理该阶段的测试被试
            for subject_id in test_subjects:
                logger.info(f"\n--- 开始处理测试被试 {subject_id:02d} ---")

                result = process_single_subject_comprehensive(subject_id, stage_number, stage_type, logger)

                stage_info = {'stage_type': stage_type, 'stage_number': stage_number}

                if result:
                    stage_results.append(result)
                    progress_monitor.update(success=True, stage_info=stage_info)
                    logger.info(f"✓ 被试 {subject_id:02d} 处理成功")
                else:
                    progress_monitor.update(success=False, stage_info=stage_info)
                    logger.error(f"✗ 被试 {subject_id:02d} 处理失败")

            # 保存该阶段的数据
            if stage_results:
                save_success = save_stage_data_to_hdf5_comprehensive(stage_results, stage_type, stage_number, logger)
                if save_success:
                    logger.info(f"✓ 阶段 {stage_type}_{stage_number} 数据保存成功")
                else:
                    logger.error(f"✗ 阶段 {stage_type}_{stage_number} 数据保存失败")
            else:
                logger.error(f"✗ 阶段 {stage_type}_{stage_number} 没有有效数据")

    # 最终报告
    progress_monitor.final_report()
    logger.info("HEP数据提取脚本测试完成!")

    # 验证输出文件
    logger.info("\n=== 验证输出文件 ===")

    # 检查HDF5文件
    hdf5_file = os.path.join(OUTPUT_DIR, "prac_01_hep_data.h5")
    if os.path.exists(hdf5_file):
        logger.info(f"✓ HDF5文件已生成: {hdf5_file}")

        # 检查文件内容
        try:
            with h5py.File(hdf5_file, 'r') as f:
                logger.info(f"  - 数据集数量: {len(f.keys())}")
                for key in f.keys():
                    if isinstance(f[key], h5py.Dataset):
                        logger.info(f"  - {key}: {f[key].shape}")
                    else:
                        logger.info(f"  - {key}: 组")

                # 检查元数据
                logger.info(f"  - 被试数量: {f.attrs.get('n_subjects', 'N/A')}")
                logger.info(f"  - 创建时间: {f.attrs.get('creation_time', 'N/A')}")
        except Exception as e:
            logger.error(f"✗ 读取HDF5文件失败: {str(e)}")
    else:
        logger.error(f"✗ HDF5文件未生成: {hdf5_file}")

    # 检查图片文件
    plots_count = 0
    if os.path.exists(PLOTS_DIR):
        for filename in os.listdir(PLOTS_DIR):
            if filename.endswith('.png') and 'three_regions_hep' in filename:
                plots_count += 1
        logger.info(f"✓ 生成可视化图片数量: {plots_count}")
    else:
        logger.error(f"✗ 图片目录不存在: {PLOTS_DIR}")

    # 检查日志文件
    if os.path.exists(LOGS_DIR):
        log_files = [f for f in os.listdir(LOGS_DIR) if f.endswith('.log')]
        logger.info(f"✓ 生成日志文件数量: {len(log_files)}")
    else:
        logger.error(f"✗ 日志目录不存在: {LOGS_DIR}")

if __name__ == "__main__":
    test_main()
