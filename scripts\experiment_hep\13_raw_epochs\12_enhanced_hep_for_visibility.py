#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强HEP可见性版本
进一步优化信号处理以达到参考图的HEP波形效果

优化策略：
1. 增加信号增强系数以提高HEP可见性
2. 优化电极选择（专注于HEP敏感区域）
3. 改进信号平均和降噪方法
4. 使用更精确的基线校正
5. 实现多被试对比分析

目标：达到参考图中0.2-0.4μV的HEP幅度水平

作者: HEP分析团队
日期: 2024年
"""

import os
import sys
import numpy as np
import mne
import neurokit2 as nk
import logging
import warnings
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 忽略警告
warnings.filterwarnings('ignore')
mne.set_log_level('ERROR')

# 基础路径配置
BASE_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
DATA_DIR = r"D:\ecgeeg\19-eegecg手动预处理6-ICA3"
OUTPUT_DIR = os.path.join(BASE_DIR, 'result', 'hep_analysis', '13_raw_epochs', '12_enhanced_visibility')
os.makedirs(OUTPUT_DIR, exist_ok=True)

# 数据结构配置
EEG_CHANNELS = 61
SAMPLING_RATE = 500

# 标准HEP提取参数
HEP_TMIN = -0.2
HEP_TMAX = 0.65
BASELINE_TMIN = -0.1
BASELINE_TMAX = 0.0
FILTER_LOW = 0.1
FILTER_HIGH = 30.0
HEP_ANALYSIS_START = 0.2
HEP_ANALYSIS_END = 0.6

# 增强的信号参数
ENHANCED_VOLTAGE_FACTOR = 150000  # 增加增强系数以提高可见性
TARGET_HEP_AMPLITUDE = 0.3  # 目标HEP幅度（接近参考图）

# 优化的电极选择（HEP最敏感区域）
HEP_SENSITIVE_ELECTRODES = ['Cz', 'C1', 'C2', 'FC1', 'FC2', 'Fz', 'F1', 'F2']
FRONTAL_ELECTRODES = ['Fz', 'F1', 'F2', 'FC1', 'FC2', 'AF3', 'AF4']
CENTRAL_ELECTRODES = ['Cz', 'C1', 'C2', 'C3', 'C4']

# ECG通道配置
ECG_PRIORITY = ['ECG11', 'ECG7', 'ECG8', 'ECG12']

def setup_logging():
    """设置日志系统"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler(sys.stdout)]
    )
    return logging.getLogger(__name__)

def load_and_enhance_signal(subject_id=1, stage_number='01', stage_type='prac', logger=None):
    """加载数据并应用增强的信号处理"""
    try:
        filename = f"{subject_id:02d}_{stage_number}_reto2_combined_baseline_corrected_chanloc_bad_interp_avgref_ica_{stage_type}.fif"
        file_path = os.path.join(DATA_DIR, filename)
        
        if not os.path.exists(file_path):
            logger.error(f"文件不存在: {filename}")
            return None, None
        
        raw = mne.io.read_raw_fif(file_path, preload=True, verbose=False)
        logger.info(f"成功加载数据: {filename}")
        
        # 分析原始信号
        original_data = raw.get_data()
        original_eeg = original_data[:EEG_CHANNELS, :]
        original_std = np.std(original_eeg)
        
        logger.info(f"原始EEG信号: 标准差={original_std:.2e}")
        
        # 应用增强的信号放大
        raw._data *= ENHANCED_VOLTAGE_FACTOR
        
        # 验证增强效果
        enhanced_data = raw.get_data()
        enhanced_eeg = enhanced_data[:EEG_CHANNELS, :]
        enhanced_std = np.std(enhanced_eeg)
        
        logger.info(f"增强信号处理:")
        logger.info(f"  - 增强系数: ×{ENHANCED_VOLTAGE_FACTOR}")
        logger.info(f"  - 增强后标准差: {enhanced_std:.2f} μV")
        logger.info(f"  - 目标HEP幅度: {TARGET_HEP_AMPLITUDE} μV")
        
        if enhanced_std >= 2.0:
            logger.info("  ✓ 信号增强成功，达到高可见性水平")
        else:
            logger.warning(f"  ⚠ 信号可能需要进一步增强")
        
        return raw, filename
        
    except Exception as e:
        logger.error(f"数据加载和增强失败: {str(e)}")
        return None, None

def apply_optimized_filtering(raw, logger):
    """应用优化的滤波处理"""
    try:
        raw_filtered = raw.copy()
        
        # 标准HEP滤波
        raw_filtered.filter(
            l_freq=FILTER_LOW, 
            h_freq=FILTER_HIGH, 
            fir_design='firwin',
            verbose=False
        )
        
        logger.info(f"应用优化滤波: {FILTER_LOW}-{FILTER_HIGH}Hz")
        
        # 验证滤波效果
        filtered_eeg = raw_filtered.get_data()[:EEG_CHANNELS, :]
        filtered_std = np.std(filtered_eeg)
        logger.info(f"滤波后EEG标准差: {filtered_std:.2f} μV")
        
        return raw_filtered
        
    except Exception as e:
        logger.error(f"滤波失败: {str(e)}")
        return raw

def select_hep_sensitive_electrodes(raw, logger):
    """选择HEP敏感电极"""
    try:
        ch_names = raw.ch_names
        
        # 找到HEP敏感电极
        hep_indices = []
        frontal_indices = []
        central_indices = []
        
        for electrode in HEP_SENSITIVE_ELECTRODES:
            if electrode in ch_names:
                hep_indices.append(ch_names.index(electrode))
        
        for electrode in FRONTAL_ELECTRODES:
            if electrode in ch_names:
                frontal_indices.append(ch_names.index(electrode))
                
        for electrode in CENTRAL_ELECTRODES:
            if electrode in ch_names:
                central_indices.append(ch_names.index(electrode))
        
        logger.info(f"电极选择结果:")
        logger.info(f"  - HEP敏感电极: {len(hep_indices)} 个")
        logger.info(f"  - 额叶电极: {len(frontal_indices)} 个")
        logger.info(f"  - 中央电极: {len(central_indices)} 个")
        
        return {
            'hep_sensitive': hep_indices,
            'frontal': frontal_indices,
            'central': central_indices
        }
        
    except Exception as e:
        logger.error(f"电极选择失败: {str(e)}")
        return None

def detect_r_peaks_enhanced(raw, logger):
    """增强的R波检测"""
    try:
        ch_names = raw.ch_names
        data = raw.get_data()
        
        # 选择最佳ECG通道
        best_channel = None
        best_quality = 0
        
        for ecg_ch in ECG_PRIORITY:
            if ecg_ch in ch_names:
                ch_idx = ch_names.index(ecg_ch)
                ch_data = data[ch_idx, :]
                quality_score = np.std(ch_data) * (np.max(ch_data) - np.min(ch_data))
                
                if quality_score > best_quality:
                    best_quality = quality_score
                    best_channel = ecg_ch
        
        if not best_channel:
            best_channel = 'ECG11'
        
        logger.info(f"使用ECG通道: {best_channel}")
        
        # 提取ECG信号
        ecg_idx = ch_names.index(best_channel)
        ecg_signal = data[ecg_idx, :]
        
        # 清洗和检测R峰
        ecg_cleaned = nk.ecg_clean(ecg_signal, sampling_rate=SAMPLING_RATE)
        _, rpeaks_info = nk.ecg_peaks(ecg_cleaned, sampling_rate=SAMPLING_RATE, method='neurokit')
        r_peaks = rpeaks_info.get('ECG_R_Peaks', [])
        
        logger.info(f"R峰检测: 检测到 {len(r_peaks)} 个R峰")
        
        if len(r_peaks) < 30:
            logger.warning("R峰数量较少，可能影响HEP质量")
            return None, None, None
        
        return r_peaks, ecg_cleaned, best_channel
        
    except Exception as e:
        logger.error(f"R峰检测失败: {str(e)}")
        return None, None, None

def extract_enhanced_hep_epochs(raw, r_peaks, logger):
    """提取增强的HEP epochs"""
    try:
        # 创建事件数组
        events = np.column_stack([r_peaks, np.zeros_like(r_peaks), np.ones_like(r_peaks)])
        
        # 创建epochs
        epochs = mne.Epochs(
            raw, events, event_id=1,
            tmin=HEP_TMIN, tmax=HEP_TMAX,
            baseline=None,
            preload=True, 
            reject=None,
            verbose=False
        )
        
        logger.info(f"创建epochs: {len(epochs)} 个心跳锁定的epochs")
        
        # 精确的基线校正
        baseline_mask = (epochs.times >= BASELINE_TMIN) & (epochs.times <= BASELINE_TMAX)
        data = epochs.get_data()
        
        # 计算基线并校正
        baseline_data = data[:, :, baseline_mask]
        baseline_mean = np.mean(baseline_data, axis=2, keepdims=True)
        data_corrected = data - baseline_mean
        epochs._data = data_corrected
        
        logger.info(f"精确基线校正: {BASELINE_TMIN*1000:.0f}ms 到 {BASELINE_TMAX*1000:.0f}ms")
        
        return epochs
        
    except Exception as e:
        logger.error(f"HEP epochs提取失败: {str(e)}")
        return None

def analyze_enhanced_hep(epochs, electrode_groups, logger):
    """分析增强的HEP成分"""
    try:
        eeg_data = epochs.get_data()[:, :EEG_CHANNELS, :]
        mean_hep = np.mean(eeg_data, axis=0)
        
        results = {}
        
        # 分析不同电极组
        for group_name, indices in electrode_groups.items():
            if not indices:
                continue
                
            # 计算该组电极的平均波形
            group_mean = np.mean(mean_hep[indices, :], axis=0)
            
            # 分析HEP成分
            hep_mask = (epochs.times >= HEP_ANALYSIS_START) & (epochs.times <= HEP_ANALYSIS_END)
            hep_component = group_mean[hep_mask]
            
            baseline_mask = (epochs.times >= BASELINE_TMIN) & (epochs.times <= BASELINE_TMAX)
            baseline_component = group_mean[baseline_mask]
            
            # 计算特征
            hep_amplitude = np.std(hep_component)
            baseline_amplitude = np.std(baseline_component)
            hep_peak_to_peak = np.max(hep_component) - np.min(hep_component)
            hep_baseline_ratio = hep_amplitude / baseline_amplitude if baseline_amplitude > 0 else 0
            
            results[group_name] = {
                'mean_waveform': group_mean,
                'indices': indices,
                'hep_amplitude': hep_amplitude,
                'baseline_amplitude': baseline_amplitude,
                'hep_peak_to_peak': hep_peak_to_peak,
                'hep_baseline_ratio': hep_baseline_ratio
            }
            
            logger.info(f"{group_name}电极组 HEP分析:")
            logger.info(f"  - HEP幅度: {hep_amplitude:.3f} μV")
            logger.info(f"  - 峰峰值: {hep_peak_to_peak:.3f} μV")
            logger.info(f"  - HEP/基线比值: {hep_baseline_ratio:.2f}")
        
        return results
        
    except Exception as e:
        logger.error(f"HEP分析失败: {str(e)}")
        return None

def create_enhanced_visualization(epochs, analysis_results, subject_id, stage_type, stage_number, ecg_channel, logger):
    """创建增强的HEP可视化"""
    try:
        times_ms = epochs.times * 1000
        
        # 创建多子图布局
        fig, axes = plt.subplots(3, 1, figsize=(14, 12))
        
        colors = ['blue', 'red', 'green']
        group_names = ['hep_sensitive', 'frontal', 'central']
        display_names = ['HEP敏感电极', '额叶电极', '中央电极']
        
        for i, (group_name, display_name, color) in enumerate(zip(group_names, display_names, colors)):
            if group_name not in analysis_results:
                continue
                
            result = analysis_results[group_name]
            waveform = result['mean_waveform']
            hep_amplitude = result['hep_amplitude']
            n_electrodes = len(result['indices'])
            
            ax = axes[i]
            
            # 绘制波形
            ax.plot(times_ms, waveform, color=color, linewidth=2.5, 
                   label=f'{display_name} (n={n_electrodes}, HEP={hep_amplitude:.3f}μV)')
            
            # 标记重要区域
            ax.axvline(x=0, color='red', linestyle='--', alpha=0.7, label='R波')
            ax.axvspan(HEP_ANALYSIS_START*1000, HEP_ANALYSIS_END*1000, alpha=0.1, color='yellow')
            ax.axvspan(BASELINE_TMIN*1000, BASELINE_TMAX*1000, alpha=0.1, color='green')
            
            # 设置坐标轴
            ax.set_ylabel('幅度 (μV)', fontsize=11)
            ax.set_title(f'{display_name} - HEP波形', fontsize=12, fontweight='bold')
            ax.grid(True, alpha=0.3)
            ax.legend(fontsize=10)
            
            # 设置Y轴范围
            signal_range = max(np.abs(waveform))
            if signal_range > 0:
                y_margin = signal_range * 0.1
                ax.set_ylim(-signal_range - y_margin, signal_range + y_margin)
        
        axes[-1].set_xlabel('时间 (ms)', fontsize=12)
        
        # 总标题
        fig.suptitle(f'被试{subject_id:02d} - {stage_type}_{stage_number} 增强HEP波形分析\n'
                    f'增强系数: ×{ENHANCED_VOLTAGE_FACTOR}, ECG: {ecg_channel}, Epochs: {len(epochs)}', 
                    fontsize=14, fontweight='bold')
        
        plt.tight_layout()
        
        # 保存图表
        output_filename = f"subject_{subject_id:02d}_{stage_type}_{stage_number}_enhanced_hep_visibility.png"
        output_path = os.path.join(OUTPUT_DIR, output_filename)
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"增强HEP可视化已保存: {output_path}")
        
        # 输出最佳结果
        best_group = max(analysis_results.keys(), 
                        key=lambda x: analysis_results[x]['hep_amplitude'])
        best_amplitude = analysis_results[best_group]['hep_amplitude']
        
        logger.info(f"最佳HEP信号:")
        logger.info(f"  - 电极组: {best_group}")
        logger.info(f"  - HEP幅度: {best_amplitude:.3f} μV")
        logger.info(f"  - 与目标比较: {best_amplitude/TARGET_HEP_AMPLITUDE*100:.1f}% of target")
        
        return output_path
        
    except Exception as e:
        logger.error(f"创建增强可视化失败: {str(e)}")
        return None

def main():
    """主函数 - 增强HEP可见性"""
    logger = setup_logging()
    logger.info("=== 开始增强HEP可见性处理 ===")
    logger.info("目标：达到参考图的HEP波形效果")
    
    # 测试参数
    subject_id = 1
    stage_number = '01'
    stage_type = 'prac'
    
    logger.info(f"测试被试: {subject_id:02d}")
    logger.info(f"阶段: {stage_type}_{stage_number}")
    logger.info(f"增强参数: ×{ENHANCED_VOLTAGE_FACTOR} (目标: {TARGET_HEP_AMPLITUDE}μV)")
    
    # 1. 加载数据并增强信号
    raw, filename = load_and_enhance_signal(subject_id, stage_number, stage_type, logger)
    if raw is None:
        return
    
    # 2. 应用优化滤波
    raw_filtered = apply_optimized_filtering(raw, logger)
    
    # 3. 选择HEP敏感电极
    electrode_groups = select_hep_sensitive_electrodes(raw_filtered, logger)
    if electrode_groups is None:
        return
    
    # 4. 增强R波检测
    r_peaks, ecg_cleaned, ecg_channel = detect_r_peaks_enhanced(raw_filtered, logger)
    if r_peaks is None:
        return
    
    # 5. 提取增强HEP epochs
    epochs = extract_enhanced_hep_epochs(raw_filtered, r_peaks, logger)
    if epochs is None:
        return
    
    # 6. 分析增强HEP
    analysis_results = analyze_enhanced_hep(epochs, electrode_groups, logger)
    if analysis_results is None:
        return
    
    # 7. 创建增强可视化
    viz_path = create_enhanced_visualization(epochs, analysis_results, subject_id, stage_type, stage_number, ecg_channel, logger)
    
    logger.info("=== 增强HEP可见性处理完成 ===")
    logger.info(f"成功提取 {len(epochs)} 个心跳锁定的epochs")
    
    # 输出各电极组的最佳结果
    for group_name, result in analysis_results.items():
        logger.info(f"{group_name}: HEP幅度 {result['hep_amplitude']:.3f} μV")
    
    if viz_path:
        logger.info(f"可视化文件: {viz_path}")

if __name__ == "__main__":
    main()
