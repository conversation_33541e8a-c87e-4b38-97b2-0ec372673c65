#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终校正的HEP波形提取
基于信号幅度调查结果，应用正确的信号增强系数

关键校正：
- 使用正确的电压转换系数（×1,000,000 或基于文献的保守增强）
- 严格按照标准HEP文献方法
- 确保HEP信号达到生理范围（1-10μV）

技术规范：
- 滤波: 0.1-30Hz FIR带通滤波
- 基线校正: -100ms到0ms窗口
- 信号增强: 基于文献的保守系数
- 时间窗口: -200ms到+650ms，重点分析200-600ms

作者: HEP分析团队
日期: 2024年
"""

import os
import sys
import numpy as np
import mne
import neurokit2 as nk
import logging
import warnings
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 忽略警告
warnings.filterwarnings('ignore')
mne.set_log_level('ERROR')

# 基础路径配置
BASE_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
DATA_DIR = r"D:\ecgeeg\19-eegecg手动预处理6-ICA3"
OUTPUT_DIR = os.path.join(BASE_DIR, 'result', 'hep_analysis', '13_raw_epochs', '11_final_corrected')
os.makedirs(OUTPUT_DIR, exist_ok=True)

# 数据结构配置
EEG_CHANNELS = 61
SAMPLING_RATE = 500

# 标准HEP提取参数
HEP_TMIN = -0.2
HEP_TMAX = 0.65
BASELINE_TMIN = -0.1
BASELINE_TMAX = 0.0
FILTER_LOW = 0.1
FILTER_HIGH = 30.0
HEP_ANALYSIS_START = 0.2
HEP_ANALYSIS_END = 0.6

# 信号增强参数（基于调查结果）
VOLTAGE_ENHANCEMENT_FACTOR = 67000  # 保守增强系数，使HEP达到2μV典型幅度
TARGET_HEP_AMPLITUDE = 2.0  # 文献中典型HEP幅度

# ECG通道和电极配置
ECG_PRIORITY = ['ECG11', 'ECG7', 'ECG8', 'ECG12']
CENTRAL_ELECTRODES = ['Cz', 'C1', 'C2', 'FC1', 'FC2', 'Fz']

def setup_logging():
    """设置日志系统"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler(sys.stdout)]
    )
    return logging.getLogger(__name__)

def load_and_enhance_data(subject_id=1, stage_number='01', stage_type='prac', logger=None):
    """加载数据并应用校正的信号增强"""
    try:
        filename = f"{subject_id:02d}_{stage_number}_reto2_combined_baseline_corrected_chanloc_bad_interp_avgref_ica_{stage_type}.fif"
        file_path = os.path.join(DATA_DIR, filename)
        
        if not os.path.exists(file_path):
            logger.error(f"文件不存在: {filename}")
            return None, None
        
        raw = mne.io.read_raw_fif(file_path, preload=True, verbose=False)
        logger.info(f"成功加载数据: {filename}")
        
        # 分析原始信号强度
        original_data = raw.get_data()
        original_eeg = original_data[:EEG_CHANNELS, :]
        original_std = np.std(original_eeg)
        
        logger.info(f"原始EEG信号分析:")
        logger.info(f"  - 原始标准差: {original_std:.2e}")
        logger.info(f"  - 原始动态范围: {np.max(original_eeg) - np.min(original_eeg):.2e}")
        
        # 应用校正的信号增强
        raw._data *= VOLTAGE_ENHANCEMENT_FACTOR
        
        # 验证增强后的信号
        enhanced_data = raw.get_data()
        enhanced_eeg = enhanced_data[:EEG_CHANNELS, :]
        enhanced_std = np.std(enhanced_eeg)
        enhanced_range = np.max(enhanced_eeg) - np.min(enhanced_eeg)
        
        logger.info(f"信号增强结果:")
        logger.info(f"  - 增强系数: ×{VOLTAGE_ENHANCEMENT_FACTOR}")
        logger.info(f"  - 增强后标准差: {enhanced_std:.2f} μV")
        logger.info(f"  - 增强后动态范围: {enhanced_range:.2f} μV")
        
        # 检查是否在生理范围内
        if 1.0 <= enhanced_std <= 50.0:
            logger.info("  ✓ 信号已达到正常生理范围")
        else:
            logger.warning(f"  ⚠ 信号可能超出正常范围: {enhanced_std:.2f} μV")
        
        return raw, filename
        
    except Exception as e:
        logger.error(f"数据加载和增强失败: {str(e)}")
        return None, None

def apply_standard_filtering(raw, logger):
    """应用标准HEP滤波"""
    try:
        raw_filtered = raw.copy()
        raw_filtered.filter(
            l_freq=FILTER_LOW, 
            h_freq=FILTER_HIGH, 
            fir_design='firwin',
            verbose=False
        )
        
        logger.info(f"应用标准HEP滤波: {FILTER_LOW}-{FILTER_HIGH}Hz (FIR)")
        
        # 验证滤波后的信号质量
        filtered_eeg = raw_filtered.get_data()[:EEG_CHANNELS, :]
        filtered_std = np.std(filtered_eeg)
        logger.info(f"滤波后EEG标准差: {filtered_std:.2f} μV")
        
        return raw_filtered
        
    except Exception as e:
        logger.error(f"滤波失败: {str(e)}")
        return raw

def select_best_ecg_channel(raw, logger):
    """选择最佳ECG通道"""
    try:
        ch_names = raw.ch_names
        data = raw.get_data()
        
        best_channel = None
        best_quality = 0
        
        logger.info("评估ECG通道质量:")
        
        for ecg_ch in ECG_PRIORITY:
            if ecg_ch in ch_names:
                ch_idx = ch_names.index(ecg_ch)
                ch_data = data[ch_idx, :]
                
                ch_std = np.std(ch_data)
                ch_range = np.max(ch_data) - np.min(ch_data)
                quality_score = ch_std * ch_range
                
                logger.info(f"  {ecg_ch}: 标准差={ch_std:.1f}μV, 动态范围={ch_range:.1f}μV, 质量分数={quality_score:.1f}")
                
                if quality_score > best_quality:
                    best_quality = quality_score
                    best_channel = ecg_ch
        
        if best_channel:
            logger.info(f"✓ 选择最佳ECG通道: {best_channel}")
            return best_channel
        else:
            logger.warning("未找到合适的ECG通道，使用ECG11")
            return 'ECG11'
            
    except Exception as e:
        logger.error(f"ECG通道选择失败: {str(e)}")
        return 'ECG11'

def detect_r_peaks_robust(raw, ecg_channel, logger):
    """稳健的R波检测"""
    try:
        ch_names = raw.ch_names
        
        if ecg_channel not in ch_names:
            logger.error(f"ECG通道 {ecg_channel} 不存在")
            return None, None
        
        # 提取ECG信号
        ecg_idx = ch_names.index(ecg_channel)
        ecg_signal = raw.get_data()[ecg_idx, :]
        
        logger.info(f"使用ECG通道: {ecg_channel}")
        logger.info(f"ECG信号统计: 标准差={np.std(ecg_signal):.1f}μV, 峰峰值={np.max(ecg_signal)-np.min(ecg_signal):.1f}μV")
        
        # 清洗ECG信号
        ecg_cleaned = nk.ecg_clean(ecg_signal, sampling_rate=SAMPLING_RATE)
        
        # R峰检测
        _, rpeaks_info = nk.ecg_peaks(ecg_cleaned, sampling_rate=SAMPLING_RATE, method='neurokit')
        r_peaks = rpeaks_info.get('ECG_R_Peaks', [])
        
        if len(r_peaks) < 30:
            logger.warning(f"R峰数量较少: {len(r_peaks)}")
            return None, None
        
        # 验证R峰质量
        rr_intervals = np.diff(r_peaks) / SAMPLING_RATE
        mean_rr = np.mean(rr_intervals)
        std_rr = np.std(rr_intervals)
        heart_rate = 60 / mean_rr
        
        logger.info(f"R峰检测结果:")
        logger.info(f"  - 检测到 {len(r_peaks)} 个R峰")
        logger.info(f"  - 平均心率: {heart_rate:.1f} bpm")
        logger.info(f"  - RR间期变异: {std_rr:.3f}s")
        
        return r_peaks, ecg_cleaned
        
    except Exception as e:
        logger.error(f"R峰检测失败: {str(e)}")
        return None, None

def extract_corrected_hep_epochs(raw, r_peaks, logger):
    """提取校正的HEP epochs"""
    try:
        # 创建事件数组
        events = np.column_stack([r_peaks, np.zeros_like(r_peaks), np.ones_like(r_peaks)])
        
        # 创建epochs
        epochs = mne.Epochs(
            raw, events, event_id=1,
            tmin=HEP_TMIN, tmax=HEP_TMAX,
            baseline=None,
            preload=True, 
            reject=None,
            verbose=False
        )
        
        logger.info(f"创建epochs: 时间窗口 {HEP_TMIN*1000:.0f}ms 到 {HEP_TMAX*1000:.0f}ms")
        logger.info(f"成功创建 {len(epochs)} 个心跳锁定的epochs")
        
        # 标准基线校正
        baseline_mask = (epochs.times >= BASELINE_TMIN) & (epochs.times <= BASELINE_TMAX)
        data = epochs.get_data()
        baseline_data = data[:, :, baseline_mask]
        baseline_mean = np.mean(baseline_data, axis=2, keepdims=True)
        data_corrected = data - baseline_mean
        epochs._data = data_corrected
        
        logger.info(f"标准基线校正: {BASELINE_TMIN*1000:.0f}ms 到 {BASELINE_TMAX*1000:.0f}ms")
        
        # 验证基线校正
        baseline_after = np.mean(data_corrected[:, :EEG_CHANNELS, baseline_mask])
        logger.info(f"基线校正后平均值: {baseline_after:.6f} μV")
        
        return epochs
        
    except Exception as e:
        logger.error(f"HEP epochs提取失败: {str(e)}")
        return None

def analyze_corrected_hep(epochs, logger):
    """分析校正后的HEP成分"""
    try:
        # 获取EEG数据
        eeg_data = epochs.get_data()[:, :EEG_CHANNELS, :]
        mean_hep = np.mean(eeg_data, axis=0)
        
        # 找到中央电极
        central_indices = []
        for electrode in CENTRAL_ELECTRODES:
            if electrode in epochs.ch_names[:EEG_CHANNELS]:
                central_indices.append(epochs.ch_names.index(electrode))
        
        if not central_indices:
            logger.warning("未找到中央电极，使用前8个EEG电极")
            central_indices = list(range(8))
        
        # 计算中央电极平均
        central_mean = np.mean(mean_hep[central_indices, :], axis=0)
        
        # 分析HEP成分
        hep_mask = (epochs.times >= HEP_ANALYSIS_START) & (epochs.times <= HEP_ANALYSIS_END)
        hep_component = central_mean[hep_mask]
        
        baseline_mask = (epochs.times >= BASELINE_TMIN) & (epochs.times <= BASELINE_TMAX)
        baseline_component = central_mean[baseline_mask]
        
        # 计算HEP特征
        hep_amplitude = np.std(hep_component)
        baseline_amplitude = np.std(baseline_component)
        hep_peak_to_peak = np.max(hep_component) - np.min(hep_component)
        hep_baseline_ratio = hep_amplitude / baseline_amplitude if baseline_amplitude > 0 else 0
        
        logger.info(f"校正后HEP成分分析 ({HEP_ANALYSIS_START*1000:.0f}-{HEP_ANALYSIS_END*1000:.0f}ms):")
        logger.info(f"  - HEP幅度(标准差): {hep_amplitude:.3f} μV")
        logger.info(f"  - 基线幅度(标准差): {baseline_amplitude:.3f} μV")
        logger.info(f"  - HEP峰峰值: {hep_peak_to_peak:.3f} μV")
        logger.info(f"  - HEP/基线比值: {hep_baseline_ratio:.2f}")
        
        # 检查是否达到预期的HEP幅度
        if hep_amplitude >= 1.0:
            logger.info("  ✓ HEP信号达到可检测水平")
        else:
            logger.warning(f"  ⚠ HEP信号仍然较弱: {hep_amplitude:.3f} μV")
        
        return {
            'central_mean': central_mean,
            'central_indices': central_indices,
            'hep_amplitude': hep_amplitude,
            'baseline_amplitude': baseline_amplitude,
            'hep_peak_to_peak': hep_peak_to_peak,
            'hep_baseline_ratio': hep_baseline_ratio
        }
        
    except Exception as e:
        logger.error(f"HEP分析失败: {str(e)}")
        return None

def create_final_hep_visualization(epochs, analysis_results, subject_id, stage_type, stage_number, ecg_channel, logger):
    """创建最终的HEP可视化"""
    try:
        central_mean = analysis_results['central_mean']
        central_indices = analysis_results['central_indices']
        hep_amplitude = analysis_results['hep_amplitude']
        
        # 创建时间轴
        times_ms = epochs.times * 1000
        
        # 创建图表
        fig, ax = plt.subplots(figsize=(14, 8))
        
        # 绘制HEP波形
        ax.plot(times_ms, central_mean, 'b-', linewidth=3, 
               label=f'中央电极平均 (n={len(central_indices)})')
        
        # 标记重要区域
        ax.axvline(x=0, color='red', linestyle='--', linewidth=2, alpha=0.8, label='R波')
        ax.axvspan(BASELINE_TMIN*1000, BASELINE_TMAX*1000, alpha=0.15, color='green', 
                  label=f'基线校正 ({BASELINE_TMIN*1000:.0f}到{BASELINE_TMAX*1000:.0f}ms)')
        ax.axvspan(HEP_ANALYSIS_START*1000, HEP_ANALYSIS_END*1000, alpha=0.15, color='yellow', 
                  label=f'HEP成分 ({HEP_ANALYSIS_START*1000:.0f}-{HEP_ANALYSIS_END*1000:.0f}ms)')
        
        # 设置坐标轴
        ax.set_xlabel('时间 (ms)', fontsize=12)
        ax.set_ylabel('幅度 (μV)', fontsize=12)
        ax.set_title(f'被试{subject_id:02d} - {stage_type}_{stage_number} 校正HEP波形\n'
                    f'增强系数: ×{VOLTAGE_ENHANCEMENT_FACTOR}, HEP幅度: {hep_amplitude:.2f}μV, '
                    f'ECG: {ecg_channel}, Epochs: {len(epochs)}', 
                    fontsize=14, fontweight='bold')
        
        ax.grid(True, alpha=0.3)
        ax.legend(fontsize=11)
        
        # 设置Y轴范围
        signal_range = max(np.abs(central_mean))
        if signal_range > 0:
            y_margin = signal_range * 0.1
            ax.set_ylim(-signal_range - y_margin, signal_range + y_margin)
        
        plt.tight_layout()
        
        # 保存图表
        output_filename = f"subject_{subject_id:02d}_{stage_type}_{stage_number}_final_corrected_hep.png"
        output_path = os.path.join(OUTPUT_DIR, output_filename)
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"最终校正HEP可视化已保存: {output_path}")
        
        # 输出详细统计
        logger.info(f"最终HEP波形统计:")
        logger.info(f"  - 全程幅度范围: {np.min(central_mean):.2f} 到 {np.max(central_mean):.2f} μV")
        logger.info(f"  - 全程标准差: {np.std(central_mean):.2f} μV")
        logger.info(f"  - 全程峰峰值: {np.max(central_mean) - np.min(central_mean):.2f} μV")
        
        return output_path
        
    except Exception as e:
        logger.error(f"创建最终可视化失败: {str(e)}")
        return None

def main():
    """主函数 - 最终校正的HEP提取"""
    logger = setup_logging()
    logger.info("=== 开始最终校正的HEP波形提取 ===")
    logger.info("应用基于调查结果的信号增强校正")
    
    # 测试参数
    subject_id = 1
    stage_number = '01'
    stage_type = 'prac'
    
    logger.info(f"测试被试: {subject_id:02d}")
    logger.info(f"阶段: {stage_type}_{stage_number}")
    logger.info(f"增强参数: ×{VOLTAGE_ENHANCEMENT_FACTOR} (目标HEP幅度: {TARGET_HEP_AMPLITUDE}μV)")
    
    # 1. 加载数据并应用信号增强
    raw, filename = load_and_enhance_data(subject_id, stage_number, stage_type, logger)
    if raw is None:
        return
    
    # 2. 应用标准滤波
    raw_filtered = apply_standard_filtering(raw, logger)
    
    # 3. 选择最佳ECG通道
    best_ecg_channel = select_best_ecg_channel(raw_filtered, logger)
    
    # 4. R波检测
    r_peaks, ecg_cleaned = detect_r_peaks_robust(raw_filtered, best_ecg_channel, logger)
    if r_peaks is None:
        return
    
    # 5. 提取校正的HEP epochs
    epochs = extract_corrected_hep_epochs(raw_filtered, r_peaks, logger)
    if epochs is None:
        return
    
    # 6. 分析校正后的HEP
    analysis_results = analyze_corrected_hep(epochs, logger)
    if analysis_results is None:
        return
    
    # 7. 创建最终可视化
    viz_path = create_final_hep_visualization(epochs, analysis_results, subject_id, stage_type, stage_number, best_ecg_channel, logger)
    
    logger.info("=== 最终校正HEP提取完成 ===")
    logger.info(f"成功提取 {len(epochs)} 个心跳锁定的epochs")
    logger.info(f"校正后HEP幅度: {analysis_results['hep_amplitude']:.2f} μV")
    logger.info(f"HEP/基线比值: {analysis_results['hep_baseline_ratio']:.2f}")
    if viz_path:
        logger.info(f"可视化文件: {viz_path}")

if __name__ == "__main__":
    main()
