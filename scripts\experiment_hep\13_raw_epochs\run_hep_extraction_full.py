#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
HEP数据提取脚本 - 完整运行版本

提供多种运行选项：
1. 运行所有阶段
2. 运行指定阶段
3. 运行指定被试
4. 继续未完成的处理

使用方法：
python run_hep_extraction_full.py [选项]

选项：
--all: 处理所有数据
--stage: 指定阶段 (如 prac_01, test_01, rest_01等)
--subjects: 指定被试范围 (如 1-5 或 1,2,5)
--continue: 继续未完成的处理
"""

import sys
import os
import argparse
import importlib.util

# 添加脚本路径
script_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(script_dir)

# 动态导入主脚本
main_script_path = os.path.join(script_dir, "03_comprehensive_hep_extraction.py")
spec = importlib.util.spec_from_file_location("comprehensive_hep_extraction", main_script_path)
comprehensive_hep = importlib.util.module_from_spec(spec)
spec.loader.exec_module(comprehensive_hep)

# 导入需要的函数和变量
setup_logging = comprehensive_hep.setup_logging
process_single_subject_comprehensive = comprehensive_hep.process_single_subject_comprehensive
save_stage_data_to_hdf5_comprehensive = comprehensive_hep.save_stage_data_to_hdf5_comprehensive
ProgressMonitorComprehensive = comprehensive_hep.ProgressMonitorComprehensive
SUBJECTS = comprehensive_hep.SUBJECTS
STAGE_CONFIG = comprehensive_hep.STAGE_CONFIG
OUTPUT_DIR = comprehensive_hep.OUTPUT_DIR

def parse_subjects(subjects_str):
    """解析被试字符串"""
    subjects = []
    if '-' in subjects_str:
        # 范围格式：1-5
        start, end = map(int, subjects_str.split('-'))
        subjects = [s for s in range(start, end + 1) if s in SUBJECTS]
    elif ',' in subjects_str:
        # 列表格式：1,2,5
        subjects = [int(s.strip()) for s in subjects_str.split(',') if int(s.strip()) in SUBJECTS]
    else:
        # 单个被试
        subject = int(subjects_str)
        if subject in SUBJECTS:
            subjects = [subject]
    return subjects

def parse_stage(stage_str):
    """解析阶段字符串"""
    if '_' in stage_str:
        stage_type, stage_number = stage_str.split('_')
        if stage_type in STAGE_CONFIG and stage_number in STAGE_CONFIG[stage_type]:
            return {stage_type: [stage_number]}
    return None

def check_existing_files():
    """检查已存在的输出文件"""
    existing_files = []
    for stage_type, stage_numbers in STAGE_CONFIG.items():
        for stage_number in stage_numbers:
            filename = f"{stage_type}_{stage_number}_hep_data.h5"
            filepath = os.path.join(OUTPUT_DIR, filename)
            if os.path.exists(filepath):
                existing_files.append((stage_type, stage_number, filepath))
    return existing_files

def run_extraction(subjects=None, stage_config=None, continue_mode=False):
    """运行HEP数据提取"""
    # 设置日志
    logger = setup_logging()
    
    # 使用默认配置如果未指定
    if subjects is None:
        subjects = SUBJECTS
    if stage_config is None:
        stage_config = STAGE_CONFIG
    
    logger.info("=== HEP数据提取脚本 - 完整运行版本 ===")
    logger.info(f"处理被试: {subjects} (共{len(subjects)}个)")
    logger.info(f"处理阶段: {stage_config}")
    
    # 检查现有文件
    if continue_mode:
        existing_files = check_existing_files()
        if existing_files:
            logger.info(f"发现{len(existing_files)}个已存在的输出文件:")
            for stage_type, stage_number, filepath in existing_files:
                logger.info(f"  - {stage_type}_{stage_number}: {filepath}")
            
            response = input("是否跳过已存在的阶段？(y/n): ")
            if response.lower() == 'y':
                # 从stage_config中移除已存在的阶段
                for stage_type, stage_number, _ in existing_files:
                    if stage_type in stage_config and stage_number in stage_config[stage_type]:
                        stage_config[stage_type].remove(stage_number)
                        if not stage_config[stage_type]:
                            del stage_config[stage_type]
                logger.info(f"更新后的处理阶段: {stage_config}")
    
    # 计算总文件数
    total_files = 0
    for stage_type, stage_numbers in stage_config.items():
        total_files += len(subjects) * len(stage_numbers)
    
    if total_files == 0:
        logger.info("没有需要处理的文件")
        return
    
    logger.info(f"预计处理文件总数: {total_files}")
    
    # 确认开始处理
    if total_files > 10:
        response = input(f"即将处理{total_files}个文件，是否继续？(y/n): ")
        if response.lower() != 'y':
            logger.info("用户取消处理")
            return
    
    progress_monitor = ProgressMonitorComprehensive(total_files, logger)
    
    # 按阶段处理数据
    for stage_type, stage_numbers in stage_config.items():
        for stage_number in stage_numbers:
            logger.info(f"\n{'='*50}")
            logger.info(f"开始处理阶段: {stage_type}_{stage_number}")
            logger.info(f"{'='*50}")
            
            stage_results = []
            
            # 处理该阶段的所有被试
            for i, subject_id in enumerate(subjects, 1):
                logger.info(f"\n--- 处理被试 {subject_id:02d} ({i}/{len(subjects)}) ---")
                
                result = process_single_subject_comprehensive(subject_id, stage_number, stage_type, logger)
                
                stage_info = {'stage_type': stage_type, 'stage_number': stage_number}
                
                if result:
                    stage_results.append(result)
                    progress_monitor.update(success=True, stage_info=stage_info)
                    logger.info(f"✓ 被试 {subject_id:02d} 处理成功")
                else:
                    progress_monitor.update(success=False, stage_info=stage_info)
                    logger.error(f"✗ 被试 {subject_id:02d} 处理失败")
            
            # 保存该阶段的数据
            if stage_results:
                logger.info(f"\n--- 保存阶段数据: {stage_type}_{stage_number} ---")
                save_success = save_stage_data_to_hdf5_comprehensive(stage_results, stage_type, stage_number, logger)
                if save_success:
                    logger.info(f"✓ 阶段 {stage_type}_{stage_number} 数据保存成功")
                    logger.info(f"✓ 成功处理: {len(stage_results)}/{len(subjects)} 个被试")
                else:
                    logger.error(f"✗ 阶段 {stage_type}_{stage_number} 数据保存失败")
            else:
                logger.error(f"✗ 阶段 {stage_type}_{stage_number} 没有有效数据")
    
    # 最终报告
    logger.info(f"\n{'='*50}")
    logger.info("处理完成")
    logger.info(f"{'='*50}")
    progress_monitor.final_report()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='HEP数据提取脚本 - 完整运行版本')
    parser.add_argument('--all', action='store_true', help='处理所有数据')
    parser.add_argument('--stage', type=str, help='指定阶段 (如 prac_01, test_01)')
    parser.add_argument('--subjects', type=str, help='指定被试范围 (如 1-5 或 1,2,5)')
    parser.add_argument('--continue', action='store_true', dest='continue_mode', help='继续未完成的处理')
    
    args = parser.parse_args()
    
    # 解析参数
    subjects = None
    stage_config = None
    
    if args.subjects:
        subjects = parse_subjects(args.subjects)
        if not subjects:
            print(f"错误: 无效的被试范围 '{args.subjects}'")
            return
        print(f"指定被试: {subjects}")
    
    if args.stage:
        stage_config = parse_stage(args.stage)
        if not stage_config:
            print(f"错误: 无效的阶段 '{args.stage}'")
            return
        print(f"指定阶段: {stage_config}")
    
    if not args.all and not args.stage and not args.subjects and not args.continue_mode:
        print("请指定运行选项:")
        print("  --all: 处理所有数据")
        print("  --stage STAGE: 指定阶段")
        print("  --subjects SUBJECTS: 指定被试")
        print("  --continue: 继续未完成的处理")
        print("\n示例:")
        print("  python run_hep_extraction_full.py --all")
        print("  python run_hep_extraction_full.py --stage prac_01")
        print("  python run_hep_extraction_full.py --subjects 1-5")
        print("  python run_hep_extraction_full.py --continue")
        return
    
    # 运行提取
    run_extraction(subjects=subjects, stage_config=stage_config, continue_mode=args.continue_mode)

if __name__ == "__main__":
    main()
