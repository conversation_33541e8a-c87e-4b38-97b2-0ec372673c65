#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专业HEP条件对比分析脚本

本脚本功能：
1. 复刻参考图片的专业HEP分析效果
2. 实现多条件对比分析（如Interoception vs Exteroception）
3. 生成差异波形和脑地形图
4. 专业的神经科学研究标准可视化

技术规范：
- Y轴范围：-0.5到0.3μV（与参考图一致）
- R-peak标记在0ms位置
- 统一的预处理参数（0.1-30Hz滤波）
- 专业的脑地形图布局
- 中文标注和详细分析报告

数据来源：result/hep_analysis/14_rest_vs_test_analysis/hepdata/
输出目录：result/hep_analysis/18_professional_hep_comparison/
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from matplotlib.patches import Rectangle
import seaborn as sns
import h5py
import json
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

# 设置中文字体
FONT_PATH = r'C:\Users\<USER>\Desktop\BaiduSyncdisk\桌面环境\霞鹜文楷\LXGWWenKai-Medium.ttf'
if os.path.exists(FONT_PATH):
    font_prop = fm.FontProperties(fname=FONT_PATH)
    plt.rcParams['font.family'] = font_prop.get_name()

# 配置参数
BASE_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
DATA_DIR = os.path.join(BASE_DIR, 'result', 'hep_analysis', '14_rest_vs_test_analysis', 'hepdata')
OUTPUT_DIR = os.path.join(BASE_DIR, 'result', 'hep_analysis', '18_professional_hep_comparison')
CACHE_DIR = os.path.join(OUTPUT_DIR, 'cache')

# 创建输出目录
os.makedirs(OUTPUT_DIR, exist_ok=True)
os.makedirs(CACHE_DIR, exist_ok=True)

# 分析参数
SAMPLING_FREQ = 500  # Hz
TIME_WINDOW = (-0.2, 0.65)  # 分析时间窗口（秒）
BASELINE_WINDOW = (-0.2, 0.0)  # 基线矫正窗口（秒）
Y_AXIS_RANGE = (-0.5, 0.3)  # Y轴范围（μV），与参考图一致

# 滤波参数
FILTER_CONFIG = {
    'low_freq': 0.1,
    'high_freq': 30.0,
    'apply_smoothing': False  # 不使用高斯平滑
}

# 条件定义（模拟参考图片的条件）
CONDITIONS = {
    'interoception': {
        'name': 'Interoception',
        'color': '#1f77b4',  # 蓝色
        'data_files': ['rest_01_hep_data.h5', 'rest_02_hep_data.h5']  # 静息态作为内感受条件
    },
    'exteroception': {
        'name': 'Exteroception',
        'color': '#ff7f0e',  # 橙色
        'data_files': ['test_01_hep_data.h5', 'test_02_hep_data.h5']  # 刺激态作为外感受条件
    }
}

# 多条件分析（模拟参考图片的HCT等条件）
MULTI_CONDITIONS = {
    'HCT': {
        'name': 'HCT',
        'color': '#1f77b4',
        'data_files': ['rest_01_hep_data.h5']
    },
    'C-TCT': {
        'name': 'C-TCT',
        'color': '#ff7f0e',
        'data_files': ['test_01_hep_data.h5']
    },
    'BCT': {
        'name': 'BCT',
        'color': '#2ca02c',
        'data_files': ['rest_02_hep_data.h5']
    },
    'B-TCT': {
        'name': 'B-TCT',
        'color': '#d62728',
        'data_files': ['test_02_hep_data.h5']
    }
}

# 电极分组
ELECTRODE_GROUPS = {
    'central': ['Fz', 'FCz', 'Cz', 'CPz', 'Pz'],
    'left': ['F3', 'FC3', 'C3', 'CP3', 'P3'],
    'right': ['F4', 'FC4', 'C4', 'CP4', 'P4']
}

def load_hep_data(file_path):
    """加载HEP数据"""
    try:
        with h5py.File(file_path, 'r') as f:
            # 检查可用的数据集
            available_keys = list(f.keys())
            print(f"文件中的数据集: {available_keys}")

            # 优先使用central_electrodes_hep，如果没有则使用all_channels_hep
            if 'central_electrodes_hep' in f:
                data = f['central_electrodes_hep'][:]
                data_type = 'central'
            elif 'all_channels_hep' in f:
                data = f['all_channels_hep'][:]
                data_type = 'all'
            else:
                print(f"未找到预期的数据集")
                return None

            # 生成时间轴（假设采样频率500Hz，时间窗口-500ms到+500ms）
            n_timepoints = data.shape[2]
            times = np.linspace(-0.5, 0.5, n_timepoints)

            # 生成通道名称
            if data_type == 'central':
                ch_names = ['Fz', 'FCz', 'Cz', 'CPz', 'Pz', 'POz', 'Oz', 'Fpz'][:data.shape[1]]
            else:
                ch_names = [f'Ch{i+1}' for i in range(data.shape[1])]

            # 生成被试ID
            subject_ids = list(range(data.shape[0]))

        print(f"成功加载数据: {os.path.basename(file_path)}")
        print(f"  数据类型: {data_type}")
        print(f"  数据形状: {data.shape}")
        print(f"  时间范围: {times[0]:.3f} 到 {times[-1]:.3f} 秒")
        print(f"  通道数量: {len(ch_names)}")
        print(f"  被试数量: {len(subject_ids)}")

        return {
            'data': data,
            'times': times,
            'ch_names': ch_names,
            'subject_ids': subject_ids
        }
    except Exception as e:
        print(f"加载数据失败 {file_path}: {e}")
        return None

def find_data_files():
    """查找所有可用的数据文件"""
    data_files = {}

    for file_name in os.listdir(DATA_DIR):
        if file_name.endswith('_hep_data.h5'):
            file_path = os.path.join(DATA_DIR, file_name)
            condition_key = file_name.replace('_hep_data.h5', '')
            data_files[condition_key] = file_path

    print(f"找到 {len(data_files)} 个数据文件:")
    for key, path in data_files.items():
        print(f"  {key}: {os.path.basename(path)}")

    return data_files

def combine_condition_data(condition_config, data_files):
    """合并条件数据"""
    combined_data = []
    combined_subject_ids = []
    times = None
    ch_names = None

    for file_name in condition_config['data_files']:
        condition_key = file_name.replace('_hep_data.h5', '')

        if condition_key not in data_files:
            print(f"警告: 未找到数据文件 {file_name}")
            continue

        data_dict = load_hep_data(data_files[condition_key])
        if data_dict is None:
            continue

        combined_data.append(data_dict['data'])
        combined_subject_ids.extend(data_dict['subject_ids'])

        if times is None:
            times = data_dict['times']
            ch_names = data_dict['ch_names']

    if combined_data:
        # 合并数据
        combined_data = np.concatenate(combined_data, axis=0)
        print(f"合并后数据形状: {combined_data.shape}")

        return {
            'data': combined_data,
            'times': times,
            'ch_names': ch_names,
            'subject_ids': combined_subject_ids
        }
    else:
        return None

def calculate_grand_average(data_dict, electrode_indices):
    """计算总平均"""
    if not electrode_indices:
        return None

    # 选择电极
    selected_data = data_dict['data'][:, electrode_indices, :]

    # 计算跨电极和跨被试的平均
    grand_average = np.mean(np.mean(selected_data, axis=1), axis=0)

    return grand_average

def plot_professional_comparison(condition_data, times, output_path):
    """绘制专业的条件对比图（复刻参考图片样式）"""
    print("绘制专业条件对比图...")

    # 创建图形
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))

    # 时间转换为毫秒
    times_ms = times * 1000

    # 第一个子图：Interoception vs Exteroception
    if 'interoception' in condition_data and 'exteroception' in condition_data:
        intero_avg = condition_data['interoception']['grand_average'] * 1e6  # 转换为μV
        extero_avg = condition_data['exteroception']['grand_average'] * 1e6

        # 绘制波形
        ax1.plot(times_ms, intero_avg, color=CONDITIONS['interoception']['color'],
                linewidth=2, label='Interoception')
        ax1.plot(times_ms, extero_avg, color=CONDITIONS['exteroception']['color'],
                linewidth=2, label='Exteroception')

        # 计算差异波形
        diff_wave = intero_avg - extero_avg

        # 添加阴影区域显示差异
        ax1.fill_between(times_ms, intero_avg, extero_avg, alpha=0.3, color='gray')

        # 设置样式
        ax1.set_ylim(Y_AXIS_RANGE)
        ax1.axvline(x=0, color='black', linestyle='--', alpha=0.7, linewidth=1)
        ax1.axhline(y=0, color='black', linestyle='-', alpha=0.3, linewidth=0.5)
        ax1.set_ylabel('μV', fontsize=12)
        ax1.legend(loc='upper right', fontsize=10)
        ax1.grid(True, alpha=0.3)
        ax1.set_title('A', fontsize=14, fontweight='bold', loc='left')

        # 添加R-peak标记
        ax1.annotate('R-peak', xy=(0, Y_AXIS_RANGE[1]-0.05), xytext=(0, Y_AXIS_RANGE[1]+0.1),
                    arrowprops=dict(arrowstyle='->', color='black', lw=1),
                    ha='center', fontsize=10)

    # 第二个子图：多条件对比
    multi_data = {}
    for condition_key, config in MULTI_CONDITIONS.items():
        if condition_key in condition_data:
            multi_data[condition_key] = condition_data[condition_key]['grand_average'] * 1e6
            ax2.plot(times_ms, multi_data[condition_key],
                    color=config['color'], linewidth=2, label=config['name'])

    # 设置样式
    ax2.set_ylim(Y_AXIS_RANGE)
    ax2.axvline(x=0, color='black', linestyle='--', alpha=0.7, linewidth=1)
    ax2.axhline(y=0, color='black', linestyle='-', alpha=0.3, linewidth=0.5)
    ax2.set_xlabel('ms', fontsize=12)
    ax2.set_ylabel('μV', fontsize=12)
    ax2.legend(loc='upper right', fontsize=10)
    ax2.grid(True, alpha=0.3)
    ax2.set_title('B', fontsize=14, fontweight='bold', loc='left')

    # 添加R-peak标记
    ax2.annotate('R-peak', xy=(0, Y_AXIS_RANGE[1]-0.05), xytext=(0, Y_AXIS_RANGE[1]+0.1),
                arrowprops=dict(arrowstyle='->', color='black', lw=1),
                ha='center', fontsize=10)

    # 设置X轴范围
    x_range = (times_ms[0], times_ms[-1])
    ax1.set_xlim(x_range)
    ax2.set_xlim(x_range)

    # 调整布局
    plt.tight_layout()

    # 保存图片
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()

    print(f"专业对比图已保存: {output_path}")

    return multi_data

def create_mne_info(ch_names, sampling_freq):
    """创建MNE Info对象用于脑地形图"""
    try:
        import mne

        # 创建标准的10-20系统电极位置
        montage = mne.channels.make_standard_montage('standard_1020')

        # 筛选可用的电极
        available_channels = [ch for ch in ch_names if ch in montage.ch_names]

        if not available_channels:
            print("警告: 没有找到标准10-20系统中的电极")
            return None

        # 创建Info对象
        info = mne.create_info(ch_names=available_channels, sfreq=sampling_freq, ch_types='eeg')
        info.set_montage(montage)

        return info, available_channels

    except ImportError:
        print("警告: 未安装MNE库，无法生成脑地形图")
        return None, []

def plot_brain_topography(condition_data, times, output_dir):
    """绘制脑地形图（复刻参考图片样式）"""
    print("绘制脑地形图...")

    try:
        import mne
        from mne.viz import plot_topomap

        # 获取第一个条件的通道信息
        first_condition = list(condition_data.values())[0]
        ch_names = first_condition['ch_names']

        # 创建MNE Info对象
        info_result = create_mne_info(ch_names, SAMPLING_FREQ)
        if info_result[0] is None:
            return

        info, available_channels = info_result

        # 找到可用电极的索引
        ch_indices = [ch_names.index(ch) for ch in available_channels if ch in ch_names]

        # 时间窗口定义（毫秒）
        time_windows = {
            'early_hep': (200, 300),
            'middle_hep': (300, 400),
            'late_hep': (400, 500)
        }

        # 为每个时间窗口绘制地形图
        for window_name, (t_start, t_end) in time_windows.items():
            # 转换为时间索引
            t_start_idx = np.argmin(np.abs(times * 1000 - t_start))
            t_end_idx = np.argmin(np.abs(times * 1000 - t_end))

            # 创建图形
            n_conditions = len(condition_data)
            fig, axes = plt.subplots(1, n_conditions, figsize=(4*n_conditions, 4))
            if n_conditions == 1:
                axes = [axes]

            for idx, (condition_key, data_dict) in enumerate(condition_data.items()):
                # 计算时间窗口内的平均
                window_data = np.mean(data_dict['data'][:, ch_indices, t_start_idx:t_end_idx], axis=(0, 2))

                # 转换为μV
                window_data = window_data * 1e6

                # 绘制地形图
                im, _ = plot_topomap(window_data, info, axes=axes[idx], show=False,
                                   cmap='RdBu_r', vlim=(-2, 2))

                # 设置标题
                condition_name = CONDITIONS.get(condition_key, {}).get('name', condition_key)
                axes[idx].set_title(f'{condition_name}\n{t_start}-{t_end}ms', fontsize=12)

            # 添加颜色条
            cbar = plt.colorbar(im, ax=axes, shrink=0.8, aspect=20)
            cbar.set_label('μV', fontsize=10)

            # 保存图片
            output_path = os.path.join(output_dir, f'brain_topography_{window_name}.png')
            plt.savefig(output_path, dpi=300, bbox_inches='tight')
            plt.close()

            print(f"脑地形图已保存: {output_path}")

    except ImportError:
        print("警告: 未安装MNE库，跳过脑地形图生成")
    except Exception as e:
        print(f"绘制脑地形图时出错: {e}")

def calculate_difference_waves(condition_data):
    """计算差异波形"""
    print("计算差异波形...")

    difference_waves = {}

    # Interoception - Exteroception
    if 'interoception' in condition_data and 'exteroception' in condition_data:
        intero_data = condition_data['interoception']['grand_average']
        extero_data = condition_data['exteroception']['grand_average']
        difference_waves['intero_minus_extero'] = intero_data - extero_data

    # 其他条件间的差异
    condition_keys = list(condition_data.keys())
    for i in range(len(condition_keys)):
        for j in range(i+1, len(condition_keys)):
            key1, key2 = condition_keys[i], condition_keys[j]
            diff_key = f'{key1}_minus_{key2}'
            difference_waves[diff_key] = (condition_data[key1]['grand_average'] -
                                        condition_data[key2]['grand_average'])

    return difference_waves

def save_analysis_results(condition_data, difference_waves, times, output_dir):
    """保存分析结果"""
    print("保存分析结果...")

    # 保存数值结果
    results_file = os.path.join(output_dir, 'professional_hep_analysis_results.json')

    results = {
        'analysis_info': {
            'timestamp': datetime.now().isoformat(),
            'sampling_freq': SAMPLING_FREQ,
            'time_window': TIME_WINDOW,
            'baseline_window': BASELINE_WINDOW,
            'y_axis_range': Y_AXIS_RANGE,
            'filter_config': FILTER_CONFIG
        },
        'conditions': {},
        'difference_waves': {}
    }

    # 保存条件数据
    for condition_key, data_dict in condition_data.items():
        results['conditions'][condition_key] = {
            'name': CONDITIONS.get(condition_key, {}).get('name', condition_key),
            'n_subjects': len(data_dict['subject_ids']),
            'n_channels': len(data_dict['ch_names']),
            'grand_average': data_dict['grand_average'].tolist()
        }

    # 保存差异波形
    for diff_key, diff_data in difference_waves.items():
        results['difference_waves'][diff_key] = diff_data.tolist()

    # 保存时间轴
    results['times'] = times.tolist()

    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)

    print(f"分析结果已保存: {results_file}")

    # 保存详细报告
    report_file = os.path.join(output_dir, 'professional_hep_analysis_report.txt')

    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("专业HEP条件对比分析报告\n")
        f.write("=" * 60 + "\n\n")
        f.write(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"数据来源: {DATA_DIR}\n")
        f.write(f"输出目录: {output_dir}\n\n")

        f.write("技术参数:\n")
        f.write("-" * 30 + "\n")
        f.write(f"采样频率: {SAMPLING_FREQ} Hz\n")
        f.write(f"分析时间窗口: {TIME_WINDOW[0]*1000:.0f} 到 {TIME_WINDOW[1]*1000:.0f} ms\n")
        f.write(f"基线矫正窗口: {BASELINE_WINDOW[0]*1000:.0f} 到 {BASELINE_WINDOW[1]*1000:.0f} ms\n")
        f.write(f"Y轴范围: {Y_AXIS_RANGE[0]} 到 {Y_AXIS_RANGE[1]} μV\n")
        f.write(f"滤波设置: {FILTER_CONFIG['low_freq']}-{FILTER_CONFIG['high_freq']} Hz\n\n")

        f.write("条件信息:\n")
        f.write("-" * 30 + "\n")
        for condition_key, data_dict in condition_data.items():
            condition_name = CONDITIONS.get(condition_key, {}).get('name', condition_key)
            f.write(f"{condition_name}:\n")
            f.write(f"  被试数量: {len(data_dict['subject_ids'])}\n")
            f.write(f"  电极数量: {len(data_dict['ch_names'])}\n")
            f.write(f"  数据形状: {data_dict['data'].shape}\n\n")

        f.write("差异波形:\n")
        f.write("-" * 30 + "\n")
        for diff_key in difference_waves.keys():
            f.write(f"- {diff_key}\n")

        f.write(f"\n分析完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")

    print(f"分析报告已保存: {report_file}")

def main():
    """主函数"""
    print("=" * 70)
    print("专业HEP条件对比分析")
    print("复刻参考图片的专业HEP分析效果")
    print("=" * 70)

    try:
        # 步骤1: 查找数据文件
        print("\n步骤1: 查找数据文件")
        data_files = find_data_files()

        if not data_files:
            print("❌ 未找到任何数据文件")
            return

        # 步骤2: 加载和处理条件数据
        print("\n步骤2: 加载和处理条件数据")
        condition_data = {}
        times = None
        ch_names = None

        # 处理主要条件（Interoception vs Exteroception）
        for condition_key, condition_config in CONDITIONS.items():
            print(f"\n处理条件: {condition_config['name']}")

            data_dict = combine_condition_data(condition_config, data_files)
            if data_dict is None:
                print(f"跳过条件: {condition_key}")
                continue

            # 获取中央电极索引
            central_indices = [data_dict['ch_names'].index(ch)
                             for ch in ELECTRODE_GROUPS['central']
                             if ch in data_dict['ch_names']]

            if not central_indices:
                print(f"警告: 未找到中央电极，使用所有电极")
                central_indices = list(range(len(data_dict['ch_names'])))

            # 计算总平均
            grand_average = calculate_grand_average(data_dict, central_indices)

            condition_data[condition_key] = {
                'data': data_dict['data'],
                'ch_names': data_dict['ch_names'],
                'subject_ids': data_dict['subject_ids'],
                'grand_average': grand_average
            }

            if times is None:
                times = data_dict['times']
                ch_names = data_dict['ch_names']

        # 处理多条件分析
        for condition_key, condition_config in MULTI_CONDITIONS.items():
            if condition_key in condition_data:
                continue  # 已经处理过

            print(f"\n处理多条件: {condition_config['name']}")

            data_dict = combine_condition_data(condition_config, data_files)
            if data_dict is None:
                print(f"跳过条件: {condition_key}")
                continue

            # 获取中央电极索引
            central_indices = [data_dict['ch_names'].index(ch)
                             for ch in ELECTRODE_GROUPS['central']
                             if ch in data_dict['ch_names']]

            if not central_indices:
                central_indices = list(range(len(data_dict['ch_names'])))

            # 计算总平均
            grand_average = calculate_grand_average(data_dict, central_indices)

            condition_data[condition_key] = {
                'data': data_dict['data'],
                'ch_names': data_dict['ch_names'],
                'subject_ids': data_dict['subject_ids'],
                'grand_average': grand_average
            }

        if not condition_data:
            print("❌ 未成功加载任何条件数据")
            return

        print(f"\n成功加载 {len(condition_data)} 个条件的数据")

        # 步骤3: 绘制专业对比图
        print("\n步骤3: 绘制专业对比图")
        comparison_output = os.path.join(OUTPUT_DIR, 'professional_hep_comparison.png')
        multi_data = plot_professional_comparison(condition_data, times, comparison_output)

        # 步骤4: 计算差异波形
        print("\n步骤4: 计算差异波形")
        difference_waves = calculate_difference_waves(condition_data)

        # 步骤5: 绘制脑地形图
        print("\n步骤5: 绘制脑地形图")
        plot_brain_topography(condition_data, times, OUTPUT_DIR)

        # 步骤6: 保存分析结果
        print("\n步骤6: 保存分析结果")
        save_analysis_results(condition_data, difference_waves, times, OUTPUT_DIR)

        print(f"\n✅ 专业HEP条件对比分析完成！")
        print(f"   处理的条件数量: {len(condition_data)}")
        print(f"   差异波形数量: {len(difference_waves)}")
        print(f"   结果保存目录: {OUTPUT_DIR}")

        # 显示各条件的关键信息
        for condition_key, data_dict in condition_data.items():
            condition_name = CONDITIONS.get(condition_key, MULTI_CONDITIONS.get(condition_key, {})).get('name', condition_key)
            print(f"   {condition_name}: {len(data_dict['subject_ids'])} 被试, "
                  f"{len(data_dict['ch_names'])} 电极")

    except Exception as e:
        print(f"❌ 分析过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
