#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
标准HEP波形提取 - 基于文献方法的最小干预处理
严格按照HEP研究标准协议，避免过度处理

技术规范:
- 滤波: 0.1-30Hz FIR带通滤波
- 基线校正: -100ms到0ms窗口
- R波对齐: 精确对齐到R波峰值
- 时间窗口: -200ms到+650ms，重点分析200-600ms
- 电压单位: mV转换为μV
- 最小干预: 保持数据的生理特征

参考: 标准HEP文献方法学
作者: HEP分析团队
日期: 2024年
"""

import os
import sys
import numpy as np
import mne
import neurokit2 as nk
import logging
import warnings
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 忽略警告
warnings.filterwarnings('ignore')
mne.set_log_level('ERROR')

# 基础路径配置
BASE_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
DATA_DIR = r"D:\ecgeeg\19-eegecg手动预处理6-ICA3"
OUTPUT_DIR = os.path.join(BASE_DIR, 'result', 'hep_analysis', '13_raw_epochs', '09_standard_extraction')
os.makedirs(OUTPUT_DIR, exist_ok=True)

# 数据结构配置
EEG_CHANNELS = 61
SAMPLING_RATE = 500

# 标准HEP提取参数 - 基于文献方法
HEP_TMIN = -0.2          # 提取窗口开始: -200ms
HEP_TMAX = 0.65          # 提取窗口结束: +650ms
BASELINE_TMIN = -0.1     # 基线校正开始: -100ms（标准方法）
BASELINE_TMAX = 0.0      # 基线校正结束: 0ms（R波位置）
FILTER_LOW = 0.1         # 标准低频截止: 0.1Hz
FILTER_HIGH = 30.0       # 标准高频截止: 30Hz
HEP_ANALYSIS_START = 0.2 # HEP成分分析开始: 200ms
HEP_ANALYSIS_END = 0.6   # HEP成分分析结束: 600ms

# ECG通道优先级（基于信号质量）
ECG_PRIORITY = ['ECG11', 'ECG7', 'ECG8', 'ECG12']

# 中央电极组（HEP主要观察区域）
CENTRAL_ELECTRODES = ['Cz', 'C1', 'C2', 'FC1', 'FC2', 'Fz']

def setup_logging():
    """设置日志系统"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler(sys.stdout)]
    )
    return logging.getLogger(__name__)

def load_preprocessed_data(subject_id=1, stage_number='01', stage_type='prac', logger=None):
    """加载预处理数据（已含ICA去伪迹）"""
    try:
        filename = f"{subject_id:02d}_{stage_number}_reto2_combined_baseline_corrected_chanloc_bad_interp_avgref_ica_{stage_type}.fif"
        file_path = os.path.join(DATA_DIR, filename)
        
        if not os.path.exists(file_path):
            logger.error(f"文件不存在: {filename}")
            return None, None
        
        raw = mne.io.read_raw_fif(file_path, preload=True, verbose=False)
        logger.info(f"成功加载预处理数据: {filename}")
        logger.info(f"数据已包含ICA去伪迹处理")
        logger.info(f"数据形状: {raw.get_data().shape}")
        logger.info(f"采样率: {raw.info['sfreq']}Hz")
        
        return raw, filename
        
    except Exception as e:
        logger.error(f"加载数据失败: {str(e)}")
        return None, None

def convert_voltage_units_minimal(raw, logger):
    """最小干预的电压单位转换（mV → μV）"""
    try:
        # 基于诊断结果，数据为mV单位
        conversion_factor = 1000.0  # mV to μV
        
        # 仅转换电压单位，不做其他处理
        raw._data *= conversion_factor
        
        logger.info(f"电压单位转换: mV → μV (×{conversion_factor})")
        
        # 验证转换后的生理范围
        eeg_data = raw.get_data()[:EEG_CHANNELS, :]
        eeg_std = np.std(eeg_data)
        eeg_range = np.max(eeg_data) - np.min(eeg_data)
        
        logger.info(f"转换后EEG信号: 标准差={eeg_std:.2f}μV, 动态范围={eeg_range:.2f}μV")
        
        # 检查是否在生理范围内（1-100μV）
        if 1.0 <= eeg_std <= 100.0:
            logger.info("✓ EEG信号在正常生理范围内")
        else:
            logger.warning(f"⚠ EEG信号可能超出正常生理范围: {eeg_std:.2f}μV")
        
        return raw
        
    except Exception as e:
        logger.error(f"电压单位转换失败: {str(e)}")
        return raw

def apply_standard_filtering(raw, logger):
    """应用标准HEP滤波（0.1-30Hz FIR）"""
    try:
        raw_filtered = raw.copy()
        
        # 应用标准HEP带通滤波
        raw_filtered.filter(
            l_freq=FILTER_LOW, 
            h_freq=FILTER_HIGH, 
            fir_design='firwin',  # FIR滤波器，避免相位失真
            verbose=False
        )
        
        logger.info(f"应用标准HEP滤波: {FILTER_LOW}-{FILTER_HIGH}Hz (FIR)")
        
        # 验证滤波效果
        eeg_data = raw_filtered.get_data()[:EEG_CHANNELS, :]
        eeg_std_filtered = np.std(eeg_data)
        logger.info(f"滤波后EEG标准差: {eeg_std_filtered:.2f}μV")
        
        return raw_filtered
        
    except Exception as e:
        logger.error(f"标准滤波失败: {str(e)}")
        return raw

def select_optimal_ecg_channel(raw, logger):
    """选择最佳ECG通道进行R波检测"""
    try:
        ch_names = raw.ch_names
        data = raw.get_data()
        
        best_channel = None
        best_quality = 0
        
        logger.info("评估ECG通道质量:")
        
        for ecg_ch in ECG_PRIORITY:
            if ecg_ch in ch_names:
                ch_idx = ch_names.index(ecg_ch)
                ch_data = data[ch_idx, :]
                
                # 计算信号质量指标
                ch_std = np.std(ch_data)
                ch_range = np.max(ch_data) - np.min(ch_data)
                
                # 简单的质量评分（标准差 × 动态范围）
                quality_score = ch_std * ch_range
                
                logger.info(f"  {ecg_ch}: 标准差={ch_std:.2f}μV, 动态范围={ch_range:.2f}μV, 质量分数={quality_score:.2f}")
                
                if quality_score > best_quality:
                    best_quality = quality_score
                    best_channel = ecg_ch
        
        if best_channel:
            logger.info(f"✓ 选择最佳ECG通道: {best_channel}")
            return best_channel
        else:
            logger.warning("未找到合适的ECG通道，使用ECG11作为默认")
            return 'ECG11'
            
    except Exception as e:
        logger.error(f"ECG通道选择失败: {str(e)}")
        return 'ECG11'

def detect_r_peaks_precise(raw, ecg_channel, logger):
    """精确R波检测和对齐"""
    try:
        ch_names = raw.ch_names
        
        if ecg_channel not in ch_names:
            logger.error(f"ECG通道 {ecg_channel} 不存在")
            return None, None
        
        # 提取ECG信号
        ecg_idx = ch_names.index(ecg_channel)
        ecg_signal = raw.get_data()[ecg_idx, :]
        
        logger.info(f"使用ECG通道: {ecg_channel}")
        logger.info(f"ECG信号统计: 标准差={np.std(ecg_signal):.2f}μV")
        
        # 清洗ECG信号（最小处理）
        ecg_cleaned = nk.ecg_clean(ecg_signal, sampling_rate=SAMPLING_RATE)
        
        # 使用标准方法检测R峰
        _, rpeaks_info = nk.ecg_peaks(ecg_cleaned, sampling_rate=SAMPLING_RATE, method='neurokit')
        r_peaks = rpeaks_info.get('ECG_R_Peaks', [])
        
        if len(r_peaks) < 30:
            logger.warning(f"R峰数量较少: {len(r_peaks)}")
            return None, None
        
        # 验证R峰检测质量
        rr_intervals = np.diff(r_peaks) / SAMPLING_RATE
        mean_rr = np.mean(rr_intervals)
        std_rr = np.std(rr_intervals)
        
        logger.info(f"R峰检测结果:")
        logger.info(f"  - 检测到 {len(r_peaks)} 个R峰")
        logger.info(f"  - 平均RR间期: {mean_rr:.3f}s")
        logger.info(f"  - RR间期变异: {std_rr:.3f}s")
        logger.info(f"  - 平均心率: {60/mean_rr:.1f} bpm")
        
        return r_peaks, ecg_cleaned
        
    except Exception as e:
        logger.error(f"R峰检测失败: {str(e)}")
        return None, None

def extract_hep_epochs_standard(raw, r_peaks, logger):
    """标准HEP epochs提取"""
    try:
        # 创建事件数组
        events = np.column_stack([r_peaks, np.zeros_like(r_peaks), np.ones_like(r_peaks)])
        
        # 创建epochs（标准时间窗口）
        epochs = mne.Epochs(
            raw, events, event_id=1,
            tmin=HEP_TMIN, tmax=HEP_TMAX,
            baseline=None,  # 稍后手动进行基线校正
            preload=True, 
            reject=None,  # 不自动拒绝，保持最小干预
            verbose=False
        )
        
        logger.info(f"创建epochs: 时间窗口 {HEP_TMIN*1000:.0f}ms 到 {HEP_TMAX*1000:.0f}ms")
        logger.info(f"成功创建 {len(epochs)} 个心跳锁定的epochs")
        
        # 应用标准基线校正（-100ms到0ms）
        baseline_mask = (epochs.times >= BASELINE_TMIN) & (epochs.times <= BASELINE_TMAX)
        data = epochs.get_data()
        
        # 计算基线平均值
        baseline_data = data[:, :, baseline_mask]
        baseline_mean = np.mean(baseline_data, axis=2, keepdims=True)
        
        # 应用基线校正
        data_corrected = data - baseline_mean
        epochs._data = data_corrected
        
        logger.info(f"标准基线校正: {BASELINE_TMIN*1000:.0f}ms 到 {BASELINE_TMAX*1000:.0f}ms")
        
        # 验证基线校正效果
        baseline_after = np.mean(data_corrected[:, :EEG_CHANNELS, baseline_mask])
        logger.info(f"基线校正后平均值: {baseline_after:.6f} μV")
        
        return epochs
        
    except Exception as e:
        logger.error(f"HEP epochs提取失败: {str(e)}")
        return None

def analyze_hep_components(epochs, logger):
    """分析HEP成分（200-600ms窗口）"""
    try:
        # 获取EEG数据
        eeg_data = epochs.get_data()[:, :EEG_CHANNELS, :]
        
        # 计算平均HEP波形
        mean_hep = np.mean(eeg_data, axis=0)
        
        # 找到中央电极
        central_indices = []
        for electrode in CENTRAL_ELECTRODES:
            if electrode in epochs.ch_names[:EEG_CHANNELS]:
                central_indices.append(epochs.ch_names.index(electrode))
        
        if not central_indices:
            logger.warning("未找到中央电极，使用所有EEG电极")
            central_indices = list(range(EEG_CHANNELS))
        
        # 计算中央电极平均
        central_mean = np.mean(mean_hep[central_indices, :], axis=0)
        
        # 分析HEP成分窗口（200-600ms）
        hep_mask = (epochs.times >= HEP_ANALYSIS_START) & (epochs.times <= HEP_ANALYSIS_END)
        hep_component = central_mean[hep_mask]
        
        # 分析基线期（用于对比）
        baseline_mask = (epochs.times >= BASELINE_TMIN) & (epochs.times <= BASELINE_TMAX)
        baseline_component = central_mean[baseline_mask]
        
        # 计算HEP特征
        hep_amplitude = np.std(hep_component)
        baseline_amplitude = np.std(baseline_component)
        hep_peak_to_peak = np.max(hep_component) - np.min(hep_component)
        
        logger.info(f"HEP成分分析 ({HEP_ANALYSIS_START*1000:.0f}-{HEP_ANALYSIS_END*1000:.0f}ms):")
        logger.info(f"  - HEP幅度(标准差): {hep_amplitude:.3f} μV")
        logger.info(f"  - 基线幅度(标准差): {baseline_amplitude:.3f} μV")
        logger.info(f"  - HEP峰峰值: {hep_peak_to_peak:.3f} μV")
        logger.info(f"  - HEP/基线比值: {hep_amplitude/baseline_amplitude if baseline_amplitude > 0 else 'N/A':.2f}")
        
        return {
            'central_mean': central_mean,
            'central_indices': central_indices,
            'hep_amplitude': hep_amplitude,
            'baseline_amplitude': baseline_amplitude,
            'hep_peak_to_peak': hep_peak_to_peak
        }
        
    except Exception as e:
        logger.error(f"HEP成分分析失败: {str(e)}")
        return None

def create_standard_hep_visualization(epochs, analysis_results, subject_id, stage_type, stage_number, ecg_channel, logger):
    """创建标准HEP可视化"""
    try:
        central_mean = analysis_results['central_mean']
        central_indices = analysis_results['central_indices']
        
        # 创建时间轴（毫秒）
        times_ms = epochs.times * 1000
        
        # 创建图表
        fig, ax = plt.subplots(figsize=(12, 6))
        
        # 绘制HEP波形
        ax.plot(times_ms, central_mean, 'b-', linewidth=2.5, 
               label=f'中央电极平均 (n={len(central_indices)})')
        
        # 标记重要时间点和区域
        ax.axvline(x=0, color='red', linestyle='--', linewidth=2, alpha=0.8, label='R波')
        
        # 标记基线校正窗口
        ax.axvspan(BASELINE_TMIN*1000, BASELINE_TMAX*1000, alpha=0.15, color='green', 
                  label=f'基线校正 ({BASELINE_TMIN*1000:.0f}到{BASELINE_TMAX*1000:.0f}ms)')
        
        # 标记HEP分析窗口
        ax.axvspan(HEP_ANALYSIS_START*1000, HEP_ANALYSIS_END*1000, alpha=0.15, color='yellow', 
                  label=f'HEP成分 ({HEP_ANALYSIS_START*1000:.0f}-{HEP_ANALYSIS_END*1000:.0f}ms)')
        
        # 设置坐标轴
        ax.set_xlabel('时间 (ms)', fontsize=12)
        ax.set_ylabel('幅度 (μV)', fontsize=12)
        ax.set_title(f'被试{subject_id:02d} - {stage_type}_{stage_number} 标准HEP波形\n'
                    f'滤波: {FILTER_LOW}-{FILTER_HIGH}Hz, 基线: {BASELINE_TMIN*1000:.0f}到{BASELINE_TMAX*1000:.0f}ms, '
                    f'ECG: {ecg_channel}, Epochs: {len(epochs)}', 
                    fontsize=13, fontweight='bold')
        
        ax.grid(True, alpha=0.3)
        ax.legend(fontsize=10)
        
        # 设置Y轴范围以突出HEP特征
        signal_range = max(np.abs(central_mean))
        if signal_range > 0:
            y_margin = signal_range * 0.1
            ax.set_ylim(-signal_range - y_margin, signal_range + y_margin)
        
        plt.tight_layout()
        
        # 保存图表
        output_filename = f"subject_{subject_id:02d}_{stage_type}_{stage_number}_standard_hep.png"
        output_path = os.path.join(OUTPUT_DIR, output_filename)
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"标准HEP可视化已保存: {output_path}")
        
        # 输出详细统计
        logger.info(f"标准HEP波形统计:")
        logger.info(f"  - 全程幅度范围: {np.min(central_mean):.3f} 到 {np.max(central_mean):.3f} μV")
        logger.info(f"  - 全程标准差: {np.std(central_mean):.3f} μV")
        logger.info(f"  - 全程峰峰值: {np.max(central_mean) - np.min(central_mean):.3f} μV")
        
        return output_path
        
    except Exception as e:
        logger.error(f"创建标准HEP可视化失败: {str(e)}")
        return None

def main():
    """主函数 - 标准HEP提取流程"""
    logger = setup_logging()
    logger.info("=== 开始标准HEP波形提取 ===")
    logger.info("基于文献方法的最小干预处理")
    
    # 测试参数
    subject_id = 1
    stage_number = '01'
    stage_type = 'prac'
    
    logger.info(f"测试被试: {subject_id:02d}")
    logger.info(f"阶段: {stage_type}_{stage_number}")
    logger.info(f"标准参数: 滤波 {FILTER_LOW}-{FILTER_HIGH}Hz, 基线 {BASELINE_TMIN*1000:.0f}到{BASELINE_TMAX*1000:.0f}ms")
    
    # 1. 加载预处理数据
    raw, filename = load_preprocessed_data(subject_id, stage_number, stage_type, logger)
    if raw is None:
        return
    
    # 2. 最小干预的电压单位转换
    raw = convert_voltage_units_minimal(raw, logger)
    
    # 3. 应用标准HEP滤波
    raw_filtered = apply_standard_filtering(raw, logger)
    
    # 4. 选择最佳ECG通道
    best_ecg_channel = select_optimal_ecg_channel(raw_filtered, logger)
    
    # 5. 精确R波检测
    r_peaks, ecg_cleaned = detect_r_peaks_precise(raw_filtered, best_ecg_channel, logger)
    if r_peaks is None:
        return
    
    # 6. 标准HEP epochs提取
    epochs = extract_hep_epochs_standard(raw_filtered, r_peaks, logger)
    if epochs is None:
        return
    
    # 7. 分析HEP成分
    analysis_results = analyze_hep_components(epochs, logger)
    if analysis_results is None:
        return
    
    # 8. 创建标准可视化
    viz_path = create_standard_hep_visualization(epochs, analysis_results, subject_id, stage_type, stage_number, best_ecg_channel, logger)
    
    logger.info("=== 标准HEP提取完成 ===")
    logger.info(f"成功提取 {len(epochs)} 个心跳锁定的epochs")
    logger.info(f"HEP成分幅度: {analysis_results['hep_amplitude']:.3f} μV")
    logger.info(f"使用ECG通道: {best_ecg_channel}")
    if viz_path:
        logger.info(f"可视化文件: {viz_path}")

if __name__ == "__main__":
    main()
