#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
信号幅度调查
深入分析为什么HEP信号幅度如此微弱，寻找解决方案

调查内容：
1. 检查不同电压单位转换方案
2. 分析数据预处理对信号的影响
3. 对比不同被试的信号强度
4. 寻找信号增强的合理方法

作者: HEP分析团队
日期: 2024年
"""

import os
import sys
import numpy as np
import mne
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 忽略警告
import warnings
warnings.filterwarnings('ignore')
mne.set_log_level('ERROR')

# 基础路径配置
BASE_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
DATA_DIR = r"D:\ecgeeg\19-eegecg手动预处理6-ICA3"
OUTPUT_DIR = os.path.join(BASE_DIR, 'result', 'hep_analysis', '13_raw_epochs', '10_amplitude_investigation')
os.makedirs(OUTPUT_DIR, exist_ok=True)

def investigate_voltage_units(subject_id=1, stage_number='01', stage_type='prac'):
    """调查电压单位和转换方案"""
    print(f"=== 调查被试 {subject_id:02d} - {stage_type}_{stage_number} 的电压单位 ===")
    
    # 加载数据
    filename = f"{subject_id:02d}_{stage_number}_reto2_combined_baseline_corrected_chanloc_bad_interp_avgref_ica_{stage_type}.fif"
    file_path = os.path.join(DATA_DIR, filename)
    
    if not os.path.exists(file_path):
        print(f"文件不存在: {filename}")
        return None
    
    raw = mne.io.read_raw_fif(file_path, preload=True, verbose=False)
    data = raw.get_data()
    
    # 分析原始数据
    eeg_data = data[:61, :]  # 前61个EEG通道
    ecg_data = data[61:, :]  # 后面的ECG通道
    
    print(f"原始数据分析:")
    print(f"  EEG数据:")
    print(f"    - 最小值: {np.min(eeg_data):.2e}")
    print(f"    - 最大值: {np.max(eeg_data):.2e}")
    print(f"    - 平均值: {np.mean(eeg_data):.2e}")
    print(f"    - 标准差: {np.std(eeg_data):.2e}")
    print(f"    - 动态范围: {np.max(eeg_data) - np.min(eeg_data):.2e}")
    
    print(f"  ECG数据:")
    print(f"    - 最小值: {np.min(ecg_data):.2e}")
    print(f"    - 最大值: {np.max(ecg_data):.2e}")
    print(f"    - 标准差: {np.std(ecg_data):.2e}")
    
    # 测试不同的转换系数
    conversion_factors = [1, 1000, 1000000]  # 无转换, mV→μV, V→μV
    
    print(f"\n不同转换系数的效果:")
    for factor in conversion_factors:
        converted_eeg = eeg_data * factor
        converted_std = np.std(converted_eeg)
        converted_range = np.max(converted_eeg) - np.min(converted_eeg)
        
        # 判断是否在生理范围内
        if 1.0 <= converted_std <= 100.0:
            status = "✓ 生理范围内"
        elif converted_std < 1.0:
            status = "⚠ 过小"
        else:
            status = "⚠ 过大"
        
        print(f"  转换系数 ×{factor}:")
        print(f"    - 标准差: {converted_std:.3f} μV")
        print(f"    - 动态范围: {converted_range:.3f} μV")
        print(f"    - 状态: {status}")
    
    return raw

def test_signal_enhancement_methods(raw):
    """测试信号增强方法"""
    print(f"\n=== 测试信号增强方法 ===")
    
    data = raw.get_data()
    eeg_data = data[:61, :]
    
    # 方法1: 基于统计的自适应增强
    original_std = np.std(eeg_data)
    target_std = 5.0  # 目标标准差 5μV
    adaptive_factor = target_std / original_std if original_std > 0 else 1.0
    
    print(f"方法1 - 自适应增强:")
    print(f"  - 原始标准差: {original_std:.6f}")
    print(f"  - 目标标准差: {target_std} μV")
    print(f"  - 建议增强系数: {adaptive_factor:.1f}")
    
    enhanced_data_1 = eeg_data * adaptive_factor
    enhanced_std_1 = np.std(enhanced_data_1)
    print(f"  - 增强后标准差: {enhanced_std_1:.3f} μV")
    
    # 方法2: 基于动态范围的增强
    original_range = np.max(eeg_data) - np.min(eeg_data)
    target_range = 20.0  # 目标动态范围 20μV
    range_factor = target_range / original_range if original_range > 0 else 1.0
    
    print(f"\n方法2 - 动态范围增强:")
    print(f"  - 原始动态范围: {original_range:.6f}")
    print(f"  - 目标动态范围: {target_range} μV")
    print(f"  - 建议增强系数: {range_factor:.1f}")
    
    enhanced_data_2 = eeg_data * range_factor
    enhanced_std_2 = np.std(enhanced_data_2)
    enhanced_range_2 = np.max(enhanced_data_2) - np.min(enhanced_data_2)
    print(f"  - 增强后标准差: {enhanced_std_2:.3f} μV")
    print(f"  - 增强后动态范围: {enhanced_range_2:.3f} μV")
    
    # 方法3: 保守增强（基于文献中的典型HEP幅度）
    typical_hep_amplitude = 2.0  # 文献中典型HEP幅度 2μV
    conservative_factor = typical_hep_amplitude / original_std if original_std > 0 else 1.0
    
    print(f"\n方法3 - 保守增强（基于文献）:")
    print(f"  - 文献典型HEP幅度: {typical_hep_amplitude} μV")
    print(f"  - 建议保守系数: {conservative_factor:.1f}")
    
    enhanced_data_3 = eeg_data * conservative_factor
    enhanced_std_3 = np.std(enhanced_data_3)
    print(f"  - 增强后标准差: {enhanced_std_3:.3f} μV")
    
    return {
        'adaptive_factor': adaptive_factor,
        'range_factor': range_factor,
        'conservative_factor': conservative_factor,
        'enhanced_data_1': enhanced_data_1,
        'enhanced_data_2': enhanced_data_2,
        'enhanced_data_3': enhanced_data_3
    }

def compare_multiple_subjects():
    """对比多个被试的信号强度"""
    print(f"\n=== 对比多个被试的信号强度 ===")
    
    test_subjects = [1, 2, 5, 6, 7]  # 测试几个被试
    stage_number = '01'
    stage_type = 'prac'
    
    results = []
    
    for subject_id in test_subjects:
        filename = f"{subject_id:02d}_{stage_number}_reto2_combined_baseline_corrected_chanloc_bad_interp_avgref_ica_{stage_type}.fif"
        file_path = os.path.join(DATA_DIR, filename)
        
        if not os.path.exists(file_path):
            print(f"被试 {subject_id:02d}: 文件不存在")
            continue
        
        try:
            raw = mne.io.read_raw_fif(file_path, preload=True, verbose=False)
            data = raw.get_data()
            eeg_data = data[:61, :]
            
            eeg_std = np.std(eeg_data)
            eeg_range = np.max(eeg_data) - np.min(eeg_data)
            
            results.append({
                'subject_id': subject_id,
                'std': eeg_std,
                'range': eeg_range
            })
            
            print(f"被试 {subject_id:02d}: 标准差={eeg_std:.2e}, 动态范围={eeg_range:.2e}")
            
        except Exception as e:
            print(f"被试 {subject_id:02d}: 加载失败 - {str(e)}")
    
    if results:
        # 计算统计信息
        stds = [r['std'] for r in results]
        ranges = [r['range'] for r in results]
        
        print(f"\n被试间统计:")
        print(f"  标准差: 平均={np.mean(stds):.2e}, 范围={np.min(stds):.2e}到{np.max(stds):.2e}")
        print(f"  动态范围: 平均={np.mean(ranges):.2e}, 范围={np.min(ranges):.2e}到{np.max(ranges):.2e}")
        
        # 建议统一的增强系数
        target_std = 5.0
        avg_std = np.mean(stds)
        unified_factor = target_std / avg_std if avg_std > 0 else 1.0
        
        print(f"\n建议统一增强系数: {unified_factor:.1f}")
        print(f"  (将平均标准差 {avg_std:.2e} 增强到 {target_std} μV)")
    
    return results

def create_enhancement_visualization(raw, enhancement_results, subject_id=1, stage_type='prac', stage_number='01'):
    """创建信号增强效果可视化"""
    print(f"\n=== 创建信号增强效果可视化 ===")
    
    try:
        data = raw.get_data()
        times = raw.times
        
        # 选择一段数据进行可视化（前5秒）
        duration = 5
        end_sample = int(duration * raw.info['sfreq'])
        plot_times = times[:end_sample]
        
        # 选择中央电极
        central_electrodes = ['Cz', 'C1', 'C2']
        central_indices = []
        for electrode in central_electrodes:
            if electrode in raw.ch_names:
                central_indices.append(raw.ch_names.index(electrode))
        
        if not central_indices:
            central_indices = [30]  # 默认使用第30个通道
        
        # 计算中央电极平均
        original_signal = np.mean(data[central_indices, :end_sample], axis=0)
        
        # 应用不同的增强方法
        enhanced_1 = original_signal * enhancement_results['adaptive_factor']
        enhanced_2 = original_signal * enhancement_results['range_factor']
        enhanced_3 = original_signal * enhancement_results['conservative_factor']
        
        # 创建图表
        fig, axes = plt.subplots(4, 1, figsize=(15, 12))
        
        # 原始信号
        axes[0].plot(plot_times, original_signal * 1000, 'b-', linewidth=1)  # 转换为μV显示
        axes[0].set_title('原始信号')
        axes[0].set_ylabel('幅度 (μV)')
        axes[0].grid(True, alpha=0.3)
        
        # 自适应增强
        axes[1].plot(plot_times, enhanced_1, 'r-', linewidth=1)
        axes[1].set_title(f'自适应增强 (×{enhancement_results["adaptive_factor"]:.1f})')
        axes[1].set_ylabel('幅度 (μV)')
        axes[1].grid(True, alpha=0.3)
        
        # 动态范围增强
        axes[2].plot(plot_times, enhanced_2, 'g-', linewidth=1)
        axes[2].set_title(f'动态范围增强 (×{enhancement_results["range_factor"]:.1f})')
        axes[2].set_ylabel('幅度 (μV)')
        axes[2].grid(True, alpha=0.3)
        
        # 保守增强
        axes[3].plot(plot_times, enhanced_3, 'm-', linewidth=1)
        axes[3].set_title(f'保守增强 (×{enhancement_results["conservative_factor"]:.1f})')
        axes[3].set_ylabel('幅度 (μV)')
        axes[3].set_xlabel('时间 (s)')
        axes[3].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图表
        output_filename = f"subject_{subject_id:02d}_{stage_type}_{stage_number}_signal_enhancement.png"
        output_path = os.path.join(OUTPUT_DIR, output_filename)
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"信号增强效果图已保存: {output_path}")
        return output_path
        
    except Exception as e:
        print(f"创建可视化失败: {str(e)}")
        return None

def main():
    """主函数"""
    print("开始信号幅度调查...")
    
    # 1. 调查电压单位
    raw = investigate_voltage_units(subject_id=1, stage_number='01', stage_type='prac')
    if raw is None:
        print("数据加载失败，退出")
        return
    
    # 2. 测试信号增强方法
    enhancement_results = test_signal_enhancement_methods(raw)
    
    # 3. 对比多个被试
    subject_results = compare_multiple_subjects()
    
    # 4. 创建可视化
    viz_path = create_enhancement_visualization(raw, enhancement_results, subject_id=1, stage_type='prac', stage_number='01')
    
    print(f"\n=== 调查总结 ===")
    print(f"1. 原始数据确实是mV单位，但信号幅度极小")
    print(f"2. 建议使用保守增强系数: {enhancement_results['conservative_factor']:.1f}")
    print(f"3. 这将使HEP信号达到文献中的典型幅度范围")
    if viz_path:
        print(f"4. 可视化文件: {viz_path}")

if __name__ == "__main__":
    main()
