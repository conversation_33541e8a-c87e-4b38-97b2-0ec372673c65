variable,all_correlation,all_p_value,stage_correlations,band_correlations
特质焦虑1,0.024060719326789846,0.014947879672342665,"{'prac': (np.float64(-0.1601754361333582), np.float64(2.116967184249539e-08)), 'test1': (np.float64(-0.16525621799352963), np.float64(1.3133326495456513e-15)), 'rest1': (np.float64(-0.06089096592679342), np.float64(0.034185928658342864)), 'test2': (np.float64(0.007462406034471289), np.float64(0.8362130472775106)), 'rest2': (np.float64(0.027986158198458455), np.float64(0.3307101879578393)), 'test3': (np.float64(0.22547019412871533), np.float64(5.189537696606995e-28)), 'rest3': (np.float64(0.010442057047629258), np.float64(0.7167079745269616))}","{'delta': (np.float64(-0.007685820070514854), np.float64(0.7282583002043644)), 'theta': (np.float64(0.015391229950010377), np.float64(0.4865521290101419)), 'alpha': (np.float64(0.05331378192722809), np.float64(0.015875313400176654)), 'beta': (np.float64(0.06760902734520827), np.float64(0.0022150447776009886)), 'gamma': (np.float64(0.07352331406441552), np.float64(0.0008743013259310999))}"
状态焦虑1,-0.02987439882862749,0.0025118908858035465,"{'prac': (np.float64(-0.02727584887565759), np.float64(0.3431357518075296)), 'test1': (np.float64(-0.14146725203260718), np.float64(8.49390727461112e-12)), 'rest1': (np.float64(-0.060172648338522663), np.float64(0.03636325625467369)), 'test2': (np.float64(0.007435241477057635), np.float64(0.8368008784134394)), 'rest2': (np.float64(0.028167236971486966), np.float64(0.3275896474058887)), 'test3': (np.float64(-0.0627248763441356), np.float64(0.00256090866772583)), 'rest3': (np.float64(0.06795393031797502), np.float64(0.01807534289519488))}","{'delta': (np.float64(-0.04948160722802318), np.float64(0.025208569593330225)), 'theta': (np.float64(-0.04999889786293511), np.float64(0.0237209431076263)), 'alpha': (np.float64(-0.044489139531595716), np.float64(0.04420604870831878)), 'beta': (np.float64(-0.02389751134936828), np.float64(0.27994477184024363)), 'gamma': (np.float64(-0.018764548981817605), np.float64(0.3962554444216179))}"
状态焦虑2,-0.09357716486190179,2.438322229851283e-21,"{'prac': (np.float64(-0.10210276490526665), np.float64(0.00037471534583114873)), 'test1': (np.float64(-0.2626488175881647), np.float64(9.434679624794925e-38)), 'rest1': (np.float64(-0.0121145049374776), np.float64(0.6737670444198413)), 'test2': (np.float64(-0.09443135183159254), np.float64(0.00874236984998233)), 'rest2': (np.float64(0.054686012117049244), np.float64(0.05720869051867883)), 'test3': (np.float64(-0.24597101658043405), np.float64(3.5357349616376e-33)), 'rest3': (np.float64(0.052911782199103204), np.float64(0.0657791229184346))}","{'delta': (np.float64(-0.043604737530872226), np.float64(0.04859887493479432)), 'theta': (np.float64(-0.143547166240473), np.float64(6.897454229344698e-11)), 'alpha': (np.float64(-0.17018491238909866), np.float64(9.202125950803932e-15)), 'beta': (np.float64(-0.17268329393258672), np.float64(3.681570300624499e-15)), 'gamma': (np.float64(-0.17022146788367032), np.float64(9.080503012676507e-15))}"
成功1,-0.0037575719974034943,0.7039389255938675,"{'prac': (np.float64(0.008531211655670161), np.float64(0.7668808409107453)), 'test1': (np.float64(0.13176231377519307), np.float64(2.055617839651378e-10)), 'rest1': (np.float64(-0.08438451177674872), np.float64(0.0033084175387437373)), 'test2': (np.float64(-0.03928459552435272), np.float64(0.27626299428252726)), 'rest2': (np.float64(-0.05539042892993029), np.float64(0.05407359202889424)), 'test3': (np.float64(0.011340727164583668), np.float64(0.5859008906665559)), 'rest3': (np.float64(-0.009347692049734627), np.float64(0.7453089789167259))}","{'delta': (np.float64(-0.04053510868021445), np.float64(0.06678077395563847)), 'theta': (np.float64(-0.005487401630751692), np.float64(0.8040877162078197)), 'alpha': (np.float64(0.01151034439609763), np.float64(0.6028242673372042)), 'beta': (np.float64(0.018559080794683512), np.float64(0.4014484592152585)), 'gamma': (np.float64(0.027353932573546645), np.float64(0.21617309070506127))}"
成功2,-0.013945270470609235,0.15843070715147603,"{'prac': (np.float64(0.06025087884288302), np.float64(0.03612053824281887)), 'test1': (np.float64(0.18470303814672936), np.float64(3.568275836260804e-19)), 'rest1': (np.float64(0.024172993843339437), np.float64(0.40084526203368936)), 'test2': (np.float64(0.07911516755864828), np.float64(0.02814695857495105)), 'rest2': (np.float64(-0.13928358139512578), np.float64(1.1520870929726286e-06)), 'test3': (np.float64(-0.03691096635365923), np.float64(0.07611675978390882)), 'rest3': (np.float64(-0.12012099598162687), np.float64(2.7984867879052494e-05))}","{'delta': (np.float64(-0.02416669899770338), np.float64(0.274561846101272)), 'theta': (np.float64(0.0015224796460689867), np.float64(0.9451298195239144)), 'alpha': (np.float64(-0.009097566741654602), np.float64(0.6808783602594559)), 'beta': (np.float64(-0.02372477800754506), np.float64(0.2834364110167248)), 'gamma': (np.float64(-0.0340392649349811), np.float64(0.12375693684087327))}"
成功3,0.16341227205946776,3.696672972395458e-62,"{'prac': (np.float64(0.06551585483961463), np.float64(0.022663142412172464)), 'test1': (np.float64(0.1304580611827447), np.float64(3.1007653440562317e-10)), 'rest1': (np.float64(-0.00576180363855172), np.float64(0.8413091788653064)), 'test2': (np.float64(0.08569248251132541), np.float64(0.01738875791447568)), 'rest2': (np.float64(-0.08534050433225156), np.float64(0.002969022166292813)), 'test3': (np.float64(0.6331483133226441), np.float64(4.7002681206812394e-259)), 'rest3': (np.float64(-0.1104297709386555), np.float64(0.00011854633939716682))}","{'delta': (np.float64(0.12023151738941669), np.float64(4.900747365193647e-08)), 'theta': (np.float64(0.23305084347815558), np.float64(1.2290806298322627e-26)), 'alpha': (np.float64(0.27667201483843384), np.float64(2.8568349642600473e-37)), 'beta': (np.float64(0.2785178157481001), np.float64(9.124589402624103e-38)), 'gamma': (np.float64(0.27171848027411255), np.float64(5.852781673775211e-36))}"
自信1,0.08934383887400907,1.3839363467573468e-19,"{'prac': (np.float64(0.02360473032029882), np.float64(0.4120123445546588)), 'test1': (np.float64(0.1622736787941976), np.float64(4.257202167709039e-15)), 'rest1': (np.float64(-0.1602617921129834), np.float64(2.079903962058915e-08)), 'test2': (np.float64(-0.19830150944595334), np.float64(2.87472992703946e-08)), 'rest2': (np.float64(-0.06484965139988526), np.float64(0.0240805300505298)), 'test3': (np.float64(0.44951756443515883), np.float64(2.733051821795726e-115)), 'rest3': (np.float64(-0.09100447939115515), np.float64(0.0015301629990621712))}","{'delta': (np.float64(0.04696371304655807), np.float64(0.0336558566487114)), 'theta': (np.float64(0.12106520370054348), np.float64(3.952116145404816e-08)), 'alpha': (np.float64(0.15702655549165595), np.float64(9.148030649537546e-13)), 'beta': (np.float64(0.17179516218524135), np.float64(5.1067875390947e-15)), 'gamma': (np.float64(0.16842860581287847), np.float64(1.7378089522638785e-14))}"
自信2,0.08280642437990167,4.9148801508299624e-17,"{'prac': (np.float64(0.08059521145931765), np.float64(0.005028918501126584)), 'test1': (np.float64(0.0987100942845236), np.float64(2.00128837669469e-06)), 'rest1': (np.float64(-0.052072879824386034), np.float64(0.07018507943534119)), 'test2': (np.float64(-0.0663341181895008), np.float64(0.06580739804143458)), 'rest2': (np.float64(-0.04901449918088434), np.float64(0.08833734188523373)), 'test3': (np.float64(0.34397812293410285), np.float64(3.7213175563716546e-65)), 'rest3': (np.float64(-0.07751871243680256), np.float64(0.006980651368203721))}","{'delta': (np.float64(0.07886858142440106), np.float64(0.00035585938241298125)), 'theta': (np.float64(0.11833439589966817), np.float64(7.953175539286709e-08)), 'alpha': (np.float64(0.12946772892026384), np.float64(4.1688217803936145e-09)), 'beta': (np.float64(0.13147557018117442), np.float64(2.382835597363386e-09)), 'gamma': (np.float64(0.12159412616504206), np.float64(3.44530118642553e-08))}"
自信3,-0.06769457375867993,7.186856190645736e-12,"{'prac': (np.float64(0.04079124855158626), np.float64(0.15617632098645431)), 'test1': (np.float64(0.1008060327359611), np.float64(1.2054848846305976e-06)), 'rest1': (np.float64(0.03306026641660056), np.float64(0.25050291882410114)), 'test2': (np.float64(-0.03236088928669139), np.float64(0.3698500593936033)), 'rest2': (np.float64(0.00027401543138580995), np.float64(0.9924028252417378)), 'test3': (np.float64(-0.3490403188040544), np.float64(3.6833563134113955e-67)), 'rest3': (np.float64(0.04777610575865059), np.float64(0.0966882677556486))}","{'delta': (np.float64(-0.026746209531060175), np.float64(0.2265548690558802)), 'theta': (np.float64(-0.08588628649461191), np.float64(0.00010037400180163969)), 'alpha': (np.float64(-0.12257057148111554), np.float64(2.6701202814736167e-08)), 'beta': (np.float64(-0.13113866902806237), np.float64(2.618852543409668e-09)), 'gamma': (np.float64(-0.14755728537336804), np.float64(1.985507772023487e-11))}"
疼痛1,-0.01962714589374813,0.04713266651005122,"{'prac': (np.float64(-0.08574964709790314), np.float64(0.0028337162704078944)), 'test1': (np.float64(-0.02175000638205175), np.float64(0.2960615233844306)), 'rest1': (np.float64(0.015298454333487112), np.float64(0.5949762807578745)), 'test2': (np.float64(0.09234811853921862), np.float64(0.010351119384577116)), 'rest2': (np.float64(0.017325601659259202), np.float64(0.547111372907365)), 'test3': (np.float64(-0.09315045895760783), np.float64(7.312977026002339e-06)), 'rest3': (np.float64(0.057703238039469286), np.float64(0.04477011548794194))}","{'delta': (np.float64(-0.015987403316712712), np.float64(0.46982846627664815)), 'theta': (np.float64(-0.03662469463588283), np.float64(0.09768573606778627)), 'alpha': (np.float64(-0.02239895995230262), np.float64(0.3112158924114273)), 'beta': (np.float64(-0.04261809001520134), np.float64(0.05392603330895443)), 'gamma': (np.float64(-0.021524386595317815), np.float64(0.33049140836845153))}"
疼痛2,-0.023566899544584533,0.017140612580769262,"{'prac': (np.float64(-0.06579180749208928), np.float64(0.022097589805366875)), 'test1': (np.float64(-0.03544356574983198), np.float64(0.08854598836376472)), 'rest1': (np.float64(0.050612684876401214), np.float64(0.07842928748762933)), 'test2': (np.float64(0.08681452825848116), np.float64(0.015968159450255565)), 'rest2': (np.float64(0.023144840523277293), np.float64(0.4211832206454582)), 'test3': (np.float64(-0.14490543011036586), np.float64(2.601904624637909e-12)), 'rest3': (np.float64(0.08704027162686263), np.float64(0.0024429162852016083))}","{'delta': (np.float64(-0.002701819299892198), np.float64(0.9027911494545992)), 'theta': (np.float64(-0.03892786597449615), np.float64(0.07833918188612107)), 'alpha': (np.float64(-0.03824708356000815), np.float64(0.08370254593636736)), 'beta': (np.float64(-0.06062747237330331), np.float64(0.006084475102073119)), 'gamma': (np.float64(-0.04402416151920759), np.float64(0.04647193214766216))}"
疼痛3,-0.011049843785802246,0.2637733331044836,"{'prac': (np.float64(-0.10727971259256838), np.float64(0.00018499799618648048)), 'test1': (np.float64(-0.01851972769558122), np.float64(0.3736279717349876)), 'rest1': (np.float64(0.05912875368308652), np.float64(0.039737184235937015)), 'test2': (np.float64(0.11065858245710129), np.float64(0.002104240148764258)), 'rest2': (np.float64(0.013048537904193361), np.float64(0.6502288569297617)), 'test3': (np.float64(-0.09040795655810957), np.float64(1.35030010602308e-05)), 'rest3': (np.float64(0.08283802346431568), np.float64(0.003932776859068028))}","{'delta': (np.float64(0.0003207342400490099), np.float64(0.9884320205068977)), 'theta': (np.float64(-0.028097152622368324), np.float64(0.20394749663057643)), 'alpha': (np.float64(-0.014395362413964143), np.float64(0.5151902198505386)), 'beta': (np.float64(-0.03155502460389176), np.float64(0.15363669095180735)), 'gamma': (np.float64(-0.013160076648348135), np.float64(0.5518914692177281))}"
坚韧,0.08035361274346348,3.971993125016876e-16,"{'prac': (np.float64(0.14647903506714915), np.float64(3.0937940857689985e-07)), 'test1': (np.float64(0.14290736185209135), np.float64(5.192755790618003e-12)), 'rest1': (np.float64(0.05095701106409696), np.float64(0.0764175426549339)), 'test2': (np.float64(-0.010493619107421704), np.float64(0.7712646641388666)), 'rest2': (np.float64(-0.020061936196810973), np.float64(0.4856748624715477)), 'test3': (np.float64(0.1784412987759183), np.float64(5.5662542106771125e-18)), 'rest3': (np.float64(-0.000809606396379613), np.float64(0.9775559945184418))}","{'delta': (np.float64(0.08242052330625238), np.float64(0.00018983109006002894)), 'theta': (np.float64(0.1171027772305142), np.float64(1.0847249428983497e-07)), 'alpha': (np.float64(0.11518824892805808), np.float64(1.7463258765493772e-07)), 'beta': (np.float64(0.12012813510727828), np.float64(5.032744011781235e-08)), 'gamma': (np.float64(0.12156758201680944), np.float64(3.4691604513218755e-08))}"
乐观,0.14278855075124974,9.765195892761245e-48,"{'prac': (np.float64(0.12374608505977043), np.float64(1.5840335120384765e-05)), 'test1': (np.float64(0.333819711983906), np.float64(3.035834693103838e-61)), 'rest1': (np.float64(0.1055763034211111), np.float64(0.00023417985991674813)), 'test2': (np.float64(0.05305591806250151), np.float64(0.14132187874396576)), 'rest2': (np.float64(-0.08393611125332667), np.float64(0.0034794588678172486)), 'test3': (np.float64(0.3622677316585804), np.float64(1.41776462772829e-72)), 'rest3': (np.float64(-0.04575602249280004), np.float64(0.11165146253569405))}","{'delta': (np.float64(0.1483190248606781), np.float64(1.561178354343504e-11)), 'theta': (np.float64(0.20788711989521838), np.float64(2.0680500855491967e-21)), 'alpha': (np.float64(0.21718737434494584), np.float64(2.878864990178964e-23)), 'beta': (np.float64(0.2148502918393445), np.float64(8.588573394795956e-23)), 'gamma': (np.float64(0.19837965627859197), np.float64(1.3294179916871433e-19))}"
力量,0.07806911976842054,2.6306060583526887e-15,"{'prac': (np.float64(0.19142733852661062), np.float64(1.8921525779660055e-11)), 'test1': (np.float64(0.10461131723245881), np.float64(4.6800317833613e-07)), 'rest1': (np.float64(0.01518563236761112), np.float64(0.5976954122639374)), 'test2': (np.float64(-0.10013417674500441), np.float64(0.005417685153528142)), 'rest2': (np.float64(-0.022989827294572263), np.float64(0.42430116615204333)), 'test3': (np.float64(0.18716824192594486), np.float64(1.1774316282669743e-19)), 'rest3': (np.float64(0.02795473103364353), np.float64(0.33125372392763536))}","{'delta': (np.float64(0.09913349087386049), np.float64(7.042242661177826e-06)), 'theta': (np.float64(0.12143396823457452), np.float64(3.591712151608551e-08)), 'alpha': (np.float64(0.10338768329712222), np.float64(2.7830807006080917e-06)), 'beta': (np.float64(0.1022215280504346), np.float64(3.6027589904545057e-06)), 'gamma': (np.float64(0.09394854984432524), np.float64(2.077734489247935e-05))}"
