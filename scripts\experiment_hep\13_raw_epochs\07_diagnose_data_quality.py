#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断数据质量和信号特征
分析为什么HEP信号不够清晰

作者: HEP分析团队
日期: 2024年
"""

import os
import sys
import numpy as np
import mne
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 忽略警告
import warnings
warnings.filterwarnings('ignore')
mne.set_log_level('ERROR')

# 基础路径配置
BASE_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
DATA_DIR = r"D:\ecgeeg\19-eegecg手动预处理6-ICA3"
OUTPUT_DIR = os.path.join(BASE_DIR, 'result', 'hep_analysis', '13_raw_epochs', '07_diagnose_quality')
os.makedirs(OUTPUT_DIR, exist_ok=True)

def load_and_analyze_data(subject_id=1, stage_number='01', stage_type='prac'):
    """加载并分析数据"""
    print(f"=== 分析被试 {subject_id:02d} - {stage_type}_{stage_number} ===")
    
    # 加载数据
    filename = f"{subject_id:02d}_{stage_number}_reto2_combined_baseline_corrected_chanloc_bad_interp_avgref_ica_{stage_type}.fif"
    file_path = os.path.join(DATA_DIR, filename)
    
    if not os.path.exists(file_path):
        print(f"文件不存在: {filename}")
        return None
    
    raw = mne.io.read_raw_fif(file_path, preload=True, verbose=False)
    print(f"成功加载数据: {filename}")
    print(f"数据形状: {raw.get_data().shape}")
    print(f"采样率: {raw.info['sfreq']}Hz")
    print(f"通道数: {len(raw.ch_names)}")
    print(f"数据时长: {raw.get_data().shape[1] / raw.info['sfreq']:.1f}秒")
    
    return raw

def analyze_channel_statistics(raw):
    """分析通道统计信息"""
    print("\n=== 通道统计分析 ===")
    
    data = raw.get_data()
    ch_names = raw.ch_names
    
    # 分析前61个EEG通道
    eeg_data = data[:61, :]
    print(f"EEG通道数: {eeg_data.shape[0]}")
    print(f"EEG数据统计:")
    print(f"  - 最小值: {np.min(eeg_data):.2e}")
    print(f"  - 最大值: {np.max(eeg_data):.2e}")
    print(f"  - 平均值: {np.mean(eeg_data):.2e}")
    print(f"  - 标准差: {np.std(eeg_data):.2e}")
    
    # 分析ECG通道
    ecg_channels = [ch for ch in ch_names if ch.startswith('ECG')]
    print(f"\nECG通道数: {len(ecg_channels)}")
    print(f"ECG通道名称: {ecg_channels[:10]}...")  # 显示前10个
    
    # 分析ECG11通道
    if 'ECG11' in ch_names:
        ecg11_idx = ch_names.index('ECG11')
        ecg11_data = data[ecg11_idx, :]
        print(f"\nECG11通道统计:")
        print(f"  - 最小值: {np.min(ecg11_data):.2e}")
        print(f"  - 最大值: {np.max(ecg11_data):.2e}")
        print(f"  - 平均值: {np.mean(ecg11_data):.2e}")
        print(f"  - 标准差: {np.std(ecg11_data):.2e}")
        print(f"  - 峰峰值: {np.max(ecg11_data) - np.min(ecg11_data):.2e}")
    else:
        print("未找到ECG11通道")
    
    return eeg_data, ecg_channels

def test_different_ecg_channels(raw):
    """测试不同的ECG通道"""
    print("\n=== 测试不同ECG通道 ===")
    
    data = raw.get_data()
    ch_names = raw.ch_names
    
    # 测试多个ECG通道
    test_channels = ['ECG7', 'ECG8', 'ECG11', 'ECG12', 'ECG24', 'ECG25']
    
    for ch_name in test_channels:
        if ch_name in ch_names:
            ch_idx = ch_names.index(ch_name)
            ch_data = data[ch_idx, :]
            
            print(f"\n{ch_name} 通道:")
            print(f"  - 标准差: {np.std(ch_data):.2e}")
            print(f"  - 峰峰值: {np.max(ch_data) - np.min(ch_data):.2e}")
            print(f"  - 动态范围: {np.max(np.abs(ch_data)):.2e}")
        else:
            print(f"{ch_name} 通道不存在")

def create_signal_visualization(raw, subject_id=1, stage_type='prac', stage_number='01'):
    """创建信号可视化"""
    print("\n=== 创建信号可视化 ===")
    
    data = raw.get_data()
    ch_names = raw.ch_names
    times = raw.times
    
    # 选择一段数据进行可视化（前10秒）
    duration = 10  # 秒
    end_sample = int(duration * raw.info['sfreq'])
    plot_times = times[:end_sample]
    
    fig, axes = plt.subplots(3, 1, figsize=(15, 10))
    
    # 1. EEG信号示例（中央电极）
    central_electrodes = ['Cz', 'C1', 'C2', 'Fz']
    eeg_indices = []
    for electrode in central_electrodes:
        if electrode in ch_names:
            eeg_indices.append(ch_names.index(electrode))
    
    if eeg_indices:
        eeg_sample = np.mean(data[eeg_indices, :end_sample], axis=0)
        axes[0].plot(plot_times, eeg_sample * 1e6, 'b-', linewidth=1)  # 转换为μV
        axes[0].set_title('EEG信号示例 (中央电极平均)')
        axes[0].set_ylabel('幅度 (μV)')
        axes[0].grid(True, alpha=0.3)
    
    # 2. ECG信号示例
    ecg_channels = ['ECG11', 'ECG7', 'ECG8', 'ECG12']
    for i, ch_name in enumerate(ecg_channels):
        if ch_name in ch_names:
            ch_idx = ch_names.index(ch_name)
            ecg_data = data[ch_idx, :end_sample]
            axes[1].plot(plot_times, ecg_data * 1e6, label=ch_name, linewidth=1)  # 转换为μV
    
    axes[1].set_title('ECG信号示例')
    axes[1].set_ylabel('幅度 (μV)')
    axes[1].legend()
    axes[1].grid(True, alpha=0.3)
    
    # 3. 信号功率谱密度
    from scipy import signal as scipy_signal
    
    if eeg_indices:
        eeg_sample = np.mean(data[eeg_indices, :], axis=0)
        freqs, psd = scipy_signal.welch(eeg_sample, fs=raw.info['sfreq'], nperseg=2048)
        axes[2].semilogy(freqs, psd)
        axes[2].set_title('EEG功率谱密度')
        axes[2].set_xlabel('频率 (Hz)')
        axes[2].set_ylabel('功率谱密度')
        axes[2].set_xlim(0, 50)
        axes[2].grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图表
    output_filename = f"subject_{subject_id:02d}_{stage_type}_{stage_number}_signal_analysis.png"
    output_path = os.path.join(OUTPUT_DIR, output_filename)
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"信号分析图已保存: {output_path}")
    return output_path

def analyze_voltage_units(raw):
    """分析电压单位"""
    print("\n=== 电压单位分析 ===")
    
    data = raw.get_data()
    
    # 分析EEG数据
    eeg_data = data[:61, :]
    eeg_std = np.std(eeg_data)
    eeg_max = np.max(np.abs(eeg_data))
    
    print(f"EEG数据分析:")
    print(f"  - 标准差: {eeg_std:.2e}")
    print(f"  - 最大绝对值: {eeg_max:.2e}")
    
    # 判断单位
    if eeg_std > 1e-3:
        print("  - 推测单位: V (伏特)")
        print("  - 建议转换系数: 1e6 (转换为μV)")
    elif eeg_std > 1e-6:
        print("  - 推测单位: mV (毫伏)")
        print("  - 建议转换系数: 1e3 (转换为μV)")
    elif eeg_std > 1e-9:
        print("  - 推测单位: μV (微伏)")
        print("  - 建议转换系数: 1 (无需转换)")
    else:
        print("  - 推测单位: 未知或异常小")
    
    # 分析ECG数据
    if 'ECG11' in raw.ch_names:
        ecg_idx = raw.ch_names.index('ECG11')
        ecg_data = data[ecg_idx, :]
        ecg_std = np.std(ecg_data)
        ecg_max = np.max(np.abs(ecg_data))
        
        print(f"\nECG11数据分析:")
        print(f"  - 标准差: {ecg_std:.2e}")
        print(f"  - 最大绝对值: {ecg_max:.2e}")
        
        if ecg_std > 1e-3:
            print("  - 推测单位: V (伏特)")
        elif ecg_std > 1e-6:
            print("  - 推测单位: mV (毫伏)")
        elif ecg_std > 1e-9:
            print("  - 推测单位: μV (微伏)")
        else:
            print("  - 推测单位: 未知或异常小")

def main():
    """主函数"""
    print("开始数据质量诊断...")
    
    # 加载数据
    raw = load_and_analyze_data(subject_id=1, stage_number='01', stage_type='prac')
    if raw is None:
        print("数据加载失败，退出")
        return
    
    # 分析通道统计
    eeg_data, ecg_channels = analyze_channel_statistics(raw)
    
    # 分析电压单位
    analyze_voltage_units(raw)
    
    # 测试不同ECG通道
    test_different_ecg_channels(raw)
    
    # 创建可视化
    viz_path = create_signal_visualization(raw, subject_id=1, stage_type='prac', stage_number='01')
    
    print("\n=== 诊断完成 ===")
    print(f"可视化文件: {viz_path}")

if __name__ == "__main__":
    main()
