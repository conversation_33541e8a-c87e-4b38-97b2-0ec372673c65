#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强HEP提取脚本 - 基于标准文献方法
参考标准HEP提取协议，优化信号可见性

主要改进：
1. 使用0.5-30Hz滤波（标准HEP频段）
2. 优化基线校正窗口（-150ms到-50ms）
3. 增强R波对齐精度
4. 保留HEP信号的生理特征
5. 减少过度平滑

作者: HEP分析团队
日期: 2024年
"""

import os
import sys
import numpy as np
import mne
import neurokit2 as nk
import h5py
import logging
import time
from scipy import signal
import warnings
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 忽略警告
warnings.filterwarnings('ignore')
mne.set_log_level('ERROR')

# 基础路径配置
BASE_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
# 使用实际的数据目录
DATA_DIR = r"D:\ecgeeg\19-eegecg手动预处理6-ICA3"
OUTPUT_DIR = os.path.join(BASE_DIR, 'result', 'hep_analysis', '13_raw_epochs', '04_enhanced_extraction')
LOGS_DIR = os.path.join(OUTPUT_DIR, 'logs')

# 创建输出目录
os.makedirs(OUTPUT_DIR, exist_ok=True)
os.makedirs(LOGS_DIR, exist_ok=True)

# 数据结构配置
TOTAL_CHANNELS = 119  # 总通道数
EEG_CHANNELS = 61     # 脑电通道数（前61个）
ECG_CHANNELS = 58     # 心电通道数（后58个）
SAMPLING_RATE = 500   # 采样率 Hz

# 增强HEP提取技术参数 - 基于标准文献方法
HEP_TMIN = -0.5       # R波前500ms（提取窗口）
HEP_TMAX = 1.5        # R波后1500ms（提取窗口）
DISPLAY_TMIN = -0.2   # 显示窗口开始：-200ms
DISPLAY_TMAX = 0.65   # 显示窗口结束：+650ms

# 优化基线校正窗口 - 避免R波影响
BASELINE_TMIN = -0.15  # 基线校正开始：-150ms
BASELINE_TMAX = -0.05  # 基线校正结束：-50ms（避免R波前期影响）

# 标准HEP滤波参数
FILTER_LOW = 0.5      # 标准HEP低频截止：0.5Hz
FILTER_HIGH = 30.0    # 标准HEP高频截止：30Hz

# 参考心电电极
REFERENCE_ECG = 'ECG11'

# 被试配置（排除03, 04, 14）
SUBJECTS = [1, 2, 5, 6, 7, 8, 9, 10, 11, 12, 13, 15, 16, 17, 18, 19, 20,
           21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32]

# 阶段配置
STAGE_CONFIG = {
    'prac': ['01'],
    'test': ['01', '02', '03'],
    'rest': ['01', '02', '03']
}

# 电极组合定义
CENTRAL_ELECTRODES = ['F1', 'F2', 'FC1', 'FC2', 'C1', 'Cz', 'C2', 'Fz']
RIGHT_HEMISPHERE = ['F2', 'F4', 'F6', 'AF4', 'AF8', 'FP2', 'FC2', 'FC4', 'FC6']
LEFT_HEMISPHERE = ['F1', 'F3', 'F5', 'AF3', 'AF7', 'FP1', 'FC1', 'FC3', 'FC5']

# 增强质量控制参数
QUALITY_THRESHOLDS = {
    'min_r_peaks': 30,              # 最少R峰数量
    'max_rr_interval': 2.0,         # 最大RR间期（秒）
    'min_rr_interval': 0.4,         # 最小RR间期（秒）
    'baseline_stability': 50.0,     # 基线稳定性阈值（μV）- 更严格
    'hep_clarity_threshold': 1.5,   # HEP成分清晰度阈值 - 更严格
    'r_peak_alignment_quality': 0.4, # R波对齐质量阈值（0-1，越高越好）
    'signal_amplitude_min': 1.0,    # 最小信号幅度（μV）
    'signal_amplitude_max': 100.0,  # 最大信号幅度（μV）
}

def setup_logging():
    """设置日志系统"""
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(LOGS_DIR, f'enhanced_hep_extraction_{timestamp}.log')

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    return logging.getLogger(__name__)

def get_file_pattern(subject_id, stage_number, stage_type):
    """根据实际文件格式生成文件名模式"""
    # 实际文件格式: {subject_id}_{stage_number}_reto2_combined_baseline_corrected_chanloc_bad_interp_avgref_ica_{stage_type}.fif
    return f"{subject_id:02d}_{stage_number}_reto2_combined_baseline_corrected_chanloc_bad_interp_avgref_ica_{stage_type}.fif"

def load_and_validate_data(subject_id, stage_number, stage_type, logger):
    """加载并验证数据文件"""
    try:
        filename = get_file_pattern(subject_id, stage_number, stage_type)
        file_path = os.path.join(DATA_DIR, filename)

        if not os.path.exists(file_path):
            logger.warning(f"文件不存在: {filename}")
            return None, None

        # 加载.fif文件
        raw = mne.io.read_raw_fif(file_path, preload=True, verbose=False)

        # 验证数据结构
        if len(raw.ch_names) != TOTAL_CHANNELS:
            logger.error(f"通道数不匹配: 期望{TOTAL_CHANNELS}，实际{len(raw.ch_names)}")
            return None, None

        if raw.info['sfreq'] != SAMPLING_RATE:
            logger.error(f"采样率不匹配: 期望{SAMPLING_RATE}Hz，实际{raw.info['sfreq']}Hz")
            return None, None

        # 验证参考心电通道存在
        if REFERENCE_ECG not in raw.ch_names:
            logger.error(f"参考心电通道 {REFERENCE_ECG} 不存在")
            return None, None

        logger.info(f"成功加载数据: {filename}")
        return raw, filename

    except Exception as e:
        logger.error(f"加载数据失败 {filename}: {str(e)}")
        return None, None

def detect_and_convert_voltage_units(raw, logger):
    """检测和转换电压单位到μV"""
    try:
        # 分别处理脑电和心电通道
        eeg_data = raw.get_data()[:EEG_CHANNELS, :1000]  # 前61个脑电通道
        ecg_data = raw.get_data()[EEG_CHANNELS:, :1000]  # 后58个心电通道

        # 脑电通道处理
        eeg_std = np.std(eeg_data)
        logger.info(f"脑电原始数据标准差: {eeg_std:.2e}")

        # 心电通道处理
        ecg_std = np.std(ecg_data)
        logger.info(f"心电原始数据标准差: {ecg_std:.2e}")

        # 脑电单位判断和转换
        if eeg_std > 1e-3:  # V单位
            eeg_factor = 1e6
            eeg_unit = "V"
        elif eeg_std > 1e-6:  # mV单位
            eeg_factor = 1e3
            eeg_unit = "mV"
        else:  # 已经是μV或更小
            eeg_factor = 1.0
            eeg_unit = "μV"

        # 心电单位判断和转换
        if ecg_std > 1e-3:  # V单位
            ecg_factor = 1e6
            ecg_unit = "V"
        elif ecg_std > 1e-6:  # mV单位
            ecg_factor = 1e3
            ecg_unit = "mV"
        else:  # 已经是μV
            ecg_factor = 1.0
            ecg_unit = "μV"

        logger.info(f"脑电单位: {eeg_unit}, 转换系数: {eeg_factor}")
        logger.info(f"心电单位: {ecg_unit}, 转换系数: {ecg_factor}")

        # 应用转换系数
        if eeg_factor != 1.0:
            raw._data[:EEG_CHANNELS, :] *= eeg_factor
            logger.info(f"脑电数据从{eeg_unit}转换为μV")

        if ecg_factor != 1.0:
            raw._data[EEG_CHANNELS:, :] *= ecg_factor
            logger.info(f"心电数据从{ecg_unit}转换为μV")

        # 验证转换后的数据范围
        converted_eeg = raw.get_data()[:EEG_CHANNELS, :]
        converted_ecg = raw.get_data()[EEG_CHANNELS:, :]

        eeg_converted_std = np.std(converted_eeg)
        ecg_converted_std = np.std(converted_ecg)

        logger.info(f"转换后脑电标准差: {eeg_converted_std:.2f}μV")
        logger.info(f"转换后心电标准差: {ecg_converted_std:.2f}μV")

        return raw, (eeg_factor, ecg_factor), (eeg_unit, ecg_unit)

    except Exception as e:
        logger.error(f"电压单位检测转换失败: {str(e)}")
        return raw, (1.0, 1.0), ("unknown", "unknown")

def apply_enhanced_filtering(raw, logger):
    """应用增强滤波 - 标准HEP频段0.5-30Hz"""
    try:
        raw_filtered = raw.copy()

        # 应用标准HEP滤波
        raw_filtered.filter(l_freq=FILTER_LOW, h_freq=FILTER_HIGH,
                           fir_design='firwin', verbose=False)

        logger.info(f"应用标准HEP滤波: {FILTER_LOW}-{FILTER_HIGH}Hz")
        return raw_filtered

    except Exception as e:
        logger.error(f"滤波失败: {str(e)}")
        return raw

def extract_ecg_signal(raw, logger):
    """提取参考心电通道ECG11信号"""
    try:
        # 提取ECG11通道数据
        ecg_idx = raw.ch_names.index(REFERENCE_ECG)
        ecg_signal = raw.get_data()[ecg_idx, :]

        logger.info(f"成功提取参考心电通道: {REFERENCE_ECG}")
        return ecg_signal

    except Exception as e:
        logger.error(f"提取心电通道失败: {str(e)}")
        return None

def detect_r_peaks_enhanced(ecg_signal, sampling_rate, logger):
    """增强R波检测 - 确保单峰检测和精确对齐"""
    try:
        # 清洗ECG信号
        ecg_cleaned = nk.ecg_clean(ecg_signal, sampling_rate=sampling_rate)

        # 使用多种方法检测R峰，选择最佳结果
        methods = ['neurokit', 'pantompkins1985', 'hamilton2002', 'christov2004']
        best_peaks = None
        best_score = 0
        best_method = None

        for method in methods:
            try:
                _, rpeaks_info = nk.ecg_peaks(ecg_cleaned, sampling_rate=sampling_rate, method=method)
                r_peaks = rpeaks_info.get('ECG_R_Peaks', [])

                if len(r_peaks) < QUALITY_THRESHOLDS['min_r_peaks']:
                    continue

                # 检查RR间期合理性
                rr_intervals = np.diff(r_peaks) / sampling_rate
                valid_rr = np.logical_and(
                    rr_intervals >= QUALITY_THRESHOLDS['min_rr_interval'],
                    rr_intervals <= QUALITY_THRESHOLDS['max_rr_interval']
                )
                valid_ratio = np.sum(valid_rr) / len(rr_intervals) if len(rr_intervals) > 0 else 0

                # 检查R峰对齐质量（峰值一致性）
                alignment_quality = check_r_peak_alignment_quality(ecg_cleaned, r_peaks, sampling_rate)

                # 计算质量分数
                quality_score = len(r_peaks) * valid_ratio * alignment_quality

                if quality_score > best_score:
                    best_score = quality_score
                    best_peaks = r_peaks
                    best_method = method

                logger.debug(f"方法{method}: {len(r_peaks)}个R峰, 有效RR比例: {valid_ratio:.3f}, "
                           f"对齐质量: {alignment_quality:.3f}, 质量分数: {quality_score:.1f}")

            except Exception as e:
                logger.debug(f"方法{method}失败: {str(e)}")
                continue

        if best_peaks is None:
            raise ValueError("所有R峰检测方法都失败")

        # 精细化R峰位置到真正的峰值点
        refined_peaks = refine_r_peak_positions(ecg_cleaned, best_peaks, sampling_rate)

        # 验证R峰对齐质量
        final_alignment_quality = check_r_peak_alignment_quality(ecg_cleaned, refined_peaks, sampling_rate)

        logger.info(f"R峰检测完成: 使用方法{best_method}, 检测到{len(refined_peaks)}个R峰")
        logger.info(f"R峰对齐质量: {final_alignment_quality:.3f}")

        return refined_peaks, ecg_cleaned

    except Exception as e:
        logger.error(f"R峰检测失败: {str(e)}")
        return None, None

def check_r_peak_alignment_quality(ecg_signal, r_peaks, sampling_rate, window_ms=50):
    """检查R峰对齐质量"""
    try:
        window_samples = int(window_ms * sampling_rate / 1000)
        peak_amplitudes = []

        for peak in r_peaks[:min(50, len(r_peaks))]:  # 检查前50个峰
            start = max(0, peak - window_samples // 2)
            end = min(len(ecg_signal), peak + window_samples // 2)

            if start < end:
                local_signal = ecg_signal[start:end]
                # 确保峰值在窗口中心附近
                center_idx = len(local_signal) // 2
                actual_peak_idx = np.argmax(local_signal)

                # 计算峰值偏移
                offset = abs(actual_peak_idx - center_idx)
                if offset <= 5:  # 允许5个样本点的偏移
                    peak_amplitudes.append(local_signal[actual_peak_idx])

        if len(peak_amplitudes) < 10:
            return 0.5  # 如果有效峰值太少，返回中等质量

        # 计算峰值一致性
        amplitude_cv = np.std(peak_amplitudes) / np.mean(peak_amplitudes) if np.mean(peak_amplitudes) > 0 else 1.0
        alignment_quality = max(0.0, 1.0 - amplitude_cv)  # CV越小，质量越高

        return alignment_quality

    except Exception:
        return 0.5

def refine_r_peak_positions(ecg_signal, r_peaks, sampling_rate, window_ms=30):
    """精确精细化R峰位置到真正的峰值点"""
    try:
        window_samples = int(window_ms * sampling_rate / 1000)
        refined_peaks = []

        for peak in r_peaks:
            start = max(0, peak - window_samples // 2)
            end = min(len(ecg_signal), peak + window_samples // 2)

            if start < end:
                local_signal = ecg_signal[start:end]

                # 找到局部最大值
                local_max_idx = np.argmax(local_signal)
                refined_peak = start + local_max_idx

                # 验证这确实是一个峰值（周围的值都比它小）
                if (local_max_idx > 0 and local_max_idx < len(local_signal) - 1 and
                    local_signal[local_max_idx] > local_signal[local_max_idx - 1] and
                    local_signal[local_max_idx] > local_signal[local_max_idx + 1]):
                    refined_peaks.append(refined_peak)
                else:
                    # 如果不是真正的峰值，保持原位置
                    refined_peaks.append(peak)
            else:
                refined_peaks.append(peak)

        return np.array(refined_peaks)

    except Exception:
        return r_peaks

def extract_hep_epochs_enhanced(raw, r_peaks, logger):
    """提取HEP epochs - 增强基线校正版本"""
    try:
        # 创建事件数组
        events = np.column_stack([r_peaks, np.zeros_like(r_peaks), np.ones_like(r_peaks)])

        # 创建epochs，先不进行基线校正
        epochs = mne.Epochs(raw, events, event_id=1,
                           tmin=HEP_TMIN, tmax=HEP_TMAX,
                           baseline=None,  # 先不进行基线校正
                           preload=True, reject=None, verbose=False)

        # 手动进行优化的基线校正
        baseline_mask = (epochs.times >= BASELINE_TMIN) & (epochs.times <= BASELINE_TMAX)

        # 获取数据
        data = epochs.get_data()  # shape: (n_epochs, n_channels, n_times)

        # 计算基线平均值
        baseline_data = data[:, :, baseline_mask]
        baseline_mean = np.mean(baseline_data, axis=2, keepdims=True)

        # 应用基线校正
        data_corrected = data - baseline_mean

        # 将校正后的数据放回epochs
        epochs._data = data_corrected

        logger.info(f"创建了 {len(epochs)} 个HEP epochs")
        logger.info(f"优化基线校正完成: 基线窗口 {BASELINE_TMIN*1000:.0f}ms 到 {BASELINE_TMAX*1000:.0f}ms")

        # 验证基线校正效果
        baseline_after = np.mean(data_corrected[:, :EEG_CHANNELS, baseline_mask])
        logger.info(f"基线校正后平均值: {baseline_after:.3f} μV")

        return epochs

    except Exception as e:
        logger.error(f"HEP epochs提取失败: {str(e)}")
        return None

def validate_hep_quality_enhanced(epochs, logger):
    """增强的HEP质量控制验证"""
    try:
        validation_results = {}

        # 1. 验证基线校正效果
        baseline_mask = (epochs.times >= BASELINE_TMIN) & (epochs.times <= BASELINE_TMAX)
        baseline_data = epochs.get_data()[:, :EEG_CHANNELS, baseline_mask]
        baseline_std = np.std(baseline_data)

        baseline_stable = baseline_std < QUALITY_THRESHOLDS['baseline_stability']
        validation_results['baseline_stability'] = {
            'std_uv': baseline_std,
            'threshold_uv': QUALITY_THRESHOLDS['baseline_stability'],
            'passed': baseline_stable
        }

        # 2. 检查HEP成分清晰度（200ms后）
        hep_mask = (epochs.times >= 0.2) & (epochs.times <= 0.6)
        hep_data = epochs.get_data()[:, :EEG_CHANNELS, hep_mask]
        hep_amplitude = np.std(hep_data)

        hep_clarity_ratio = hep_amplitude / baseline_std if baseline_std > 0 else 0
        hep_clear = hep_clarity_ratio >= QUALITY_THRESHOLDS['hep_clarity_threshold']
        validation_results['hep_clarity'] = {
            'amplitude_uv': hep_amplitude,
            'clarity_ratio': hep_clarity_ratio,
            'threshold': QUALITY_THRESHOLDS['hep_clarity_threshold'],
            'passed': hep_clear
        }

        # 3. 检查信号幅度范围
        all_data = epochs.get_data()[:, :EEG_CHANNELS, :]
        signal_amplitude = np.std(all_data)
        amplitude_valid = (QUALITY_THRESHOLDS['signal_amplitude_min'] <= signal_amplitude <=
                          QUALITY_THRESHOLDS['signal_amplitude_max'])
        validation_results['signal_amplitude'] = {
            'amplitude_uv': signal_amplitude,
            'min_threshold': QUALITY_THRESHOLDS['signal_amplitude_min'],
            'max_threshold': QUALITY_THRESHOLDS['signal_amplitude_max'],
            'passed': amplitude_valid
        }

        # 4. R波对齐质量检查
        zero_time_idx = np.argmin(np.abs(epochs.times))  # 找到最接近0ms的时间点
        r_wave_data = epochs.get_data()[:, EEG_CHANNELS:, zero_time_idx]  # 使用心电通道
        r_wave_consistency = np.std(r_wave_data) / (np.mean(np.abs(r_wave_data)) + 1e-10)

        # 计算对齐质量分数（CV的倒数）
        alignment_quality = 1.0 / (1.0 + r_wave_consistency)
        alignment_good = alignment_quality >= QUALITY_THRESHOLDS['r_peak_alignment_quality']
        validation_results['r_peak_alignment'] = {
            'consistency_cv': r_wave_consistency,
            'alignment_quality': alignment_quality,
            'threshold': QUALITY_THRESHOLDS['r_peak_alignment_quality'],
            'passed': alignment_good
        }

        # 总体质量评估
        all_passed = all(result['passed'] for result in validation_results.values())
        validation_results['overall_quality'] = {
            'passed': all_passed,
            'summary': f"基线稳定: {baseline_stable}, HEP清晰: {hep_clear}, "
                      f"信号幅度: {amplitude_valid}, R波对齐: {alignment_good}"
        }

        logger.info(f"HEP质量验证完成: {validation_results['overall_quality']['summary']}")

        return validation_results

    except Exception as e:
        logger.error(f"HEP质量验证失败: {str(e)}")
        return None

def save_hep_data_enhanced(epochs, subject_id, stage_type, stage_number, validation_results, logger):
    """保存增强HEP数据到HDF5文件"""
    try:
        # 创建输出文件名
        output_filename = f"subject_{subject_id:02d}_{stage_type}_{stage_number}_enhanced_hep.h5"
        output_path = os.path.join(OUTPUT_DIR, output_filename)

        with h5py.File(output_path, 'w') as f:
            # 保存epochs数据
            f.create_dataset('hep_data', data=epochs.get_data())
            f.create_dataset('times', data=epochs.times)
            f.create_dataset('ch_names', data=[ch.encode('utf-8') for ch in epochs.ch_names])

            # 保存采样率和其他元信息
            f.attrs['sampling_rate'] = SAMPLING_RATE
            f.attrs['n_epochs'] = len(epochs)
            f.attrs['n_channels'] = len(epochs.ch_names)
            f.attrs['tmin'] = HEP_TMIN
            f.attrs['tmax'] = HEP_TMAX
            f.attrs['baseline_tmin'] = BASELINE_TMIN
            f.attrs['baseline_tmax'] = BASELINE_TMAX
            f.attrs['filter_low'] = FILTER_LOW
            f.attrs['filter_high'] = FILTER_HIGH

            # 保存质量验证结果
            if validation_results:
                quality_group = f.create_group('quality_validation')
                for key, value in validation_results.items():
                    if isinstance(value, dict):
                        subgroup = quality_group.create_group(key)
                        for subkey, subvalue in value.items():
                            if isinstance(subvalue, (int, float, bool)):
                                subgroup.attrs[subkey] = subvalue
                            else:
                                subgroup.attrs[subkey] = str(subvalue)

            # 保存电极组合信息
            electrode_group = f.create_group('electrode_groups')
            electrode_group.create_dataset('central', data=[ch.encode('utf-8') for ch in CENTRAL_ELECTRODES])
            electrode_group.create_dataset('left_hemisphere', data=[ch.encode('utf-8') for ch in LEFT_HEMISPHERE])
            electrode_group.create_dataset('right_hemisphere', data=[ch.encode('utf-8') for ch in RIGHT_HEMISPHERE])

        logger.info(f"HEP数据已保存到: {output_path}")
        return output_path

    except Exception as e:
        logger.error(f"保存HEP数据失败: {str(e)}")
        return None

def process_single_subject_enhanced(subject_id, stage_type, stage_number, logger):
    """处理单个被试的增强HEP提取"""
    try:
        logger.info(f"开始处理被试 {subject_id:02d} - {stage_type}_{stage_number}")

        # 1. 加载并验证数据
        raw, filename = load_and_validate_data(subject_id, stage_number, stage_type, logger)
        if raw is None:
            logger.error(f"被试 {subject_id:02d} - {stage_type}_{stage_number} 数据加载失败")
            return None

        # 2. 检测和转换电压单位
        raw, conversion_factors, original_units = detect_and_convert_voltage_units(raw, logger)

        # 3. 应用增强滤波
        raw_filtered = apply_enhanced_filtering(raw, logger)

        # 4. 提取心电信号
        ecg_signal = extract_ecg_signal(raw_filtered, logger)
        if ecg_signal is None:
            logger.error(f"被试 {subject_id:02d} - {stage_type}_{stage_number} 心电信号提取失败")
            return None

        # 5. 增强R波检测
        r_peaks, ecg_cleaned = detect_r_peaks_enhanced(ecg_signal, SAMPLING_RATE, logger)
        if r_peaks is None:
            logger.error(f"被试 {subject_id:02d} - {stage_type}_{stage_number} R波检测失败")
            return None

        # 6. 提取HEP epochs
        epochs = extract_hep_epochs_enhanced(raw_filtered, r_peaks, logger)
        if epochs is None:
            logger.error(f"被试 {subject_id:02d} - {stage_type}_{stage_number} HEP epochs提取失败")
            return None

        # 7. 质量验证
        validation_results = validate_hep_quality_enhanced(epochs, logger)
        if validation_results is None:
            logger.warning(f"被试 {subject_id:02d} - {stage_type}_{stage_number} 质量验证失败")

        # 8. 保存数据
        output_path = save_hep_data_enhanced(epochs, subject_id, stage_type, stage_number,
                                           validation_results, logger)
        if output_path is None:
            logger.error(f"被试 {subject_id:02d} - {stage_type}_{stage_number} 数据保存失败")
            return None

        # 9. 返回处理结果
        result = {
            'subject_id': subject_id,
            'stage_type': stage_type,
            'stage_number': stage_number,
            'filename': filename,
            'output_path': output_path,
            'n_epochs': len(epochs),
            'n_r_peaks': len(r_peaks),
            'conversion_factors': conversion_factors,
            'original_units': original_units,
            'validation_results': validation_results,
            'processing_success': True
        }

        logger.info(f"被试 {subject_id:02d} - {stage_type}_{stage_number} 处理完成")
        logger.info(f"  - 提取了 {len(epochs)} 个HEP epochs")
        logger.info(f"  - 检测了 {len(r_peaks)} 个R峰")
        if validation_results and validation_results.get('overall_quality'):
            logger.info(f"  - 质量验证: {validation_results['overall_quality']['passed']}")

        return result

    except Exception as e:
        logger.error(f"被试 {subject_id:02d} - {stage_type}_{stage_number} 处理失败: {str(e)}")
        return {
            'subject_id': subject_id,
            'stage_type': stage_type,
            'stage_number': stage_number,
            'processing_success': False,
            'error': str(e)
        }

def main():
    """主函数 - 批量处理所有被试"""
    logger = setup_logging()
    logger.info("开始增强HEP提取处理")
    logger.info(f"使用参数: 滤波 {FILTER_LOW}-{FILTER_HIGH}Hz, 基线校正 {BASELINE_TMIN*1000:.0f}ms到{BASELINE_TMAX*1000:.0f}ms")

    all_results = []
    success_count = 0
    total_count = 0

    # 处理所有被试和阶段
    for subject_id in SUBJECTS:
        for stage_type, stage_numbers in STAGE_CONFIG.items():
            for stage_number in stage_numbers:
                total_count += 1

                result = process_single_subject_enhanced(subject_id, stage_type, stage_number, logger)
                all_results.append(result)

                if result and result.get('processing_success', False):
                    success_count += 1

    # 生成处理报告
    logger.info(f"\n=== 增强HEP提取处理完成 ===")
    logger.info(f"总处理数量: {total_count}")
    logger.info(f"成功处理: {success_count}")
    logger.info(f"失败处理: {total_count - success_count}")
    logger.info(f"成功率: {success_count/total_count*100:.1f}%")

    # 保存处理报告
    report_path = os.path.join(OUTPUT_DIR, 'enhanced_hep_extraction_report.txt')
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("增强HEP提取处理报告\n")
        f.write("=" * 50 + "\n\n")
        f.write(f"处理时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"滤波参数: {FILTER_LOW}-{FILTER_HIGH}Hz\n")
        f.write(f"基线校正: {BASELINE_TMIN*1000:.0f}ms到{BASELINE_TMAX*1000:.0f}ms\n\n")
        f.write(f"总处理数量: {total_count}\n")
        f.write(f"成功处理: {success_count}\n")
        f.write(f"失败处理: {total_count - success_count}\n")
        f.write(f"成功率: {success_count/total_count*100:.1f}%\n\n")

        f.write("详细结果:\n")
        f.write("-" * 30 + "\n")
        for result in all_results:
            if result:
                status = "成功" if result.get('processing_success', False) else "失败"
                f.write(f"被试{result['subject_id']:02d}_{result['stage_type']}_{result['stage_number']}: {status}\n")
                if result.get('processing_success', False):
                    f.write(f"  - Epochs: {result.get('n_epochs', 'N/A')}\n")
                    f.write(f"  - R峰: {result.get('n_r_peaks', 'N/A')}\n")
                    if result.get('validation_results') and result['validation_results'].get('overall_quality'):
                        quality_passed = result['validation_results']['overall_quality']['passed']
                        f.write(f"  - 质量验证: {'通过' if quality_passed else '未通过'}\n")
                else:
                    f.write(f"  - 错误: {result.get('error', 'Unknown')}\n")

    logger.info(f"处理报告已保存到: {report_path}")
    return all_results

if __name__ == "__main__":
    results = main()
