#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
参考图片样式的HEP分析脚本

本脚本功能：
1. 完全复刻参考图片的视觉效果
2. 生成Interoception vs Exteroception对比
3. 生成多条件HEP波形对比
4. 生成差异波形的脑地形图
5. 专业的神经科学研究标准可视化

技术规范：
- Y轴范围：-0.5到0.3μV（与参考图一致）
- R-peak标记在0ms位置
- 差异波形计算和可视化
- 专业的脑地形图布局（右侧小图）

数据来源：result/hep_analysis/14_rest_vs_test_analysis/hepdata/
输出目录：result/hep_analysis/18_professional_hep_comparison/
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from matplotlib.patches import Rectangle
import matplotlib.gridspec as gridspec
import h5py
import json
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))

# 设置中文字体
FONT_PATH = r'C:\Users\<USER>\Desktop\BaiduSyncdisk\桌面环境\霞鹜文楷\LXGWWenKai-Medium.ttf'
if os.path.exists(FONT_PATH):
    font_prop = fm.FontProperties(fname=FONT_PATH)
    plt.rcParams['font.family'] = font_prop.get_name()

# 配置参数
BASE_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
DATA_DIR = os.path.join(BASE_DIR, 'result', 'hep_analysis', '14_rest_vs_test_analysis', 'hepdata')
OUTPUT_DIR = os.path.join(BASE_DIR, 'result', 'hep_analysis', '18_professional_hep_comparison')

# 创建输出目录
os.makedirs(OUTPUT_DIR, exist_ok=True)

# 分析参数
SAMPLING_FREQ = 500  # Hz
Y_AXIS_RANGE = (-0.5, 0.3)  # Y轴范围（μV），与参考图一致

def load_hep_data(file_path):
    """加载HEP数据"""
    try:
        with h5py.File(file_path, 'r') as f:
            # 使用中央电极数据
            data = f['central_electrodes_hep'][:]
            
            # 生成时间轴（-500ms到+500ms）
            n_timepoints = data.shape[2]
            times = np.linspace(-0.5, 0.5, n_timepoints)
            
            # 生成通道名称
            ch_names = ['Fz', 'FCz', 'Cz', 'CPz', 'Pz', 'POz', 'Oz', 'Fpz'][:data.shape[1]]
            
        print(f"成功加载数据: {os.path.basename(file_path)}")
        print(f"  数据形状: {data.shape}")
        print(f"  时间范围: {times[0]:.3f} 到 {times[-1]:.3f} 秒")
        
        return {
            'data': data,
            'times': times,
            'ch_names': ch_names
        }
    except Exception as e:
        print(f"加载数据失败 {file_path}: {e}")
        return None

def calculate_grand_average(data):
    """计算总平均"""
    # 计算跨电极和跨被试的平均
    grand_average = np.mean(np.mean(data, axis=1), axis=0)
    return grand_average

def create_reference_style_plot():
    """创建参考图片样式的HEP对比图"""
    print("创建参考图片样式的HEP对比图...")
    
    # 加载数据
    rest_data = load_hep_data(os.path.join(DATA_DIR, 'rest_01_hep_data.h5'))
    test_data = load_hep_data(os.path.join(DATA_DIR, 'test_01_hep_data.h5'))
    
    if rest_data is None or test_data is None:
        print("数据加载失败")
        return
    
    # 计算总平均
    rest_avg = calculate_grand_average(rest_data['data']) * 1e6  # 转换为μV
    test_avg = calculate_grand_average(test_data['data']) * 1e6
    
    # 时间轴（毫秒）
    times_ms = rest_data['times'] * 1000
    
    # 创建图形 - 复刻参考图片的布局
    fig = plt.figure(figsize=(14, 10))
    
    # 创建网格布局
    gs = gridspec.GridSpec(2, 4, figure=fig, 
                          width_ratios=[3, 1, 1, 1], 
                          height_ratios=[1, 1],
                          hspace=0.3, wspace=0.3)
    
    # 第一个子图：Interoception vs Exteroception
    ax1 = fig.add_subplot(gs[0, 0])
    
    # 绘制波形
    ax1.plot(times_ms, rest_avg, color='#1f77b4', linewidth=2.5, label='Interoception')
    ax1.plot(times_ms, test_avg, color='#ff7f0e', linewidth=2.5, label='Exteroception')
    
    # 计算差异波形
    diff_wave = rest_avg - test_avg
    
    # 添加阴影区域显示差异
    ax1.fill_between(times_ms, rest_avg, test_avg, alpha=0.2, color='gray')
    
    # 设置样式
    ax1.set_ylim(Y_AXIS_RANGE)
    ax1.set_xlim(-100, 600)
    ax1.axvline(x=0, color='black', linestyle='--', alpha=0.7, linewidth=1)
    ax1.axhline(y=0, color='black', linestyle='-', alpha=0.3, linewidth=0.5)
    ax1.set_ylabel('μV', fontsize=12)
    ax1.set_xlabel('ms', fontsize=12)
    ax1.legend(loc='upper right', fontsize=10)
    ax1.grid(True, alpha=0.3)
    ax1.set_title('A', fontsize=16, fontweight='bold', loc='left')
    
    # 添加R-peak标记
    ax1.annotate('R-peak', xy=(0, Y_AXIS_RANGE[1]-0.05), xytext=(0, Y_AXIS_RANGE[1]+0.1),
                arrowprops=dict(arrowstyle='->', color='black', lw=1),
                ha='center', fontsize=10)
    
    # 右侧添加差异波形的脑地形图
    try:
        import mne
        from mne.viz import plot_topomap
        
        # 创建MNE Info对象
        montage = mne.channels.make_standard_montage('standard_1020')
        available_channels = [ch for ch in rest_data['ch_names'] if ch in montage.ch_names]
        
        if available_channels:
            info = mne.create_info(ch_names=available_channels, sfreq=SAMPLING_FREQ, ch_types='eeg')
            info.set_montage(montage)
            
            # 找到可用电极的索引
            ch_indices = [rest_data['ch_names'].index(ch) for ch in available_channels if ch in rest_data['ch_names']]
            
            # 计算300-400ms时间窗口的差异
            t_start_idx = np.argmin(np.abs(rest_data['times'] * 1000 - 300))
            t_end_idx = np.argmin(np.abs(rest_data['times'] * 1000 - 400))
            
            rest_window = np.mean(rest_data['data'][:, ch_indices, t_start_idx:t_end_idx], axis=(0, 2)) * 1e6
            test_window = np.mean(test_data['data'][:, ch_indices, t_start_idx:t_end_idx], axis=(0, 2)) * 1e6
            diff_topo = rest_window - test_window
            
            # 绘制地形图
            ax_topo1 = fig.add_subplot(gs[0, 1])
            im1, _ = plot_topomap(diff_topo, info, axes=ax_topo1, show=False,
                                cmap='RdBu_r', vlim=(-1, 1), contours=6)
            ax_topo1.set_title('Interoception - Exteroception\nμV', fontsize=10)
            
    except ImportError:
        print("MNE库未安装，跳过脑地形图")
    
    # 第二个子图：多条件对比
    ax2 = fig.add_subplot(gs[1, 0])
    
    # 加载更多条件数据
    conditions = {
        'HCT': {'file': 'rest_01_hep_data.h5', 'color': '#1f77b4'},
        'C-TCT': {'file': 'test_01_hep_data.h5', 'color': '#ff7f0e'},
        'BCT': {'file': 'rest_02_hep_data.h5', 'color': '#2ca02c'},
        'B-TCT': {'file': 'test_02_hep_data.h5', 'color': '#d62728'}
    }
    
    condition_data = {}
    for name, config in conditions.items():
        data = load_hep_data(os.path.join(DATA_DIR, config['file']))
        if data is not None:
            avg = calculate_grand_average(data['data']) * 1e6
            condition_data[name] = avg
            ax2.plot(times_ms, avg, color=config['color'], linewidth=2.5, label=name)
    
    # 设置样式
    ax2.set_ylim(Y_AXIS_RANGE)
    ax2.set_xlim(-100, 600)
    ax2.axvline(x=0, color='black', linestyle='--', alpha=0.7, linewidth=1)
    ax2.axhline(y=0, color='black', linestyle='-', alpha=0.3, linewidth=0.5)
    ax2.set_xlabel('ms', fontsize=12)
    ax2.set_ylabel('μV', fontsize=12)
    ax2.legend(loc='upper right', fontsize=10)
    ax2.grid(True, alpha=0.3)
    ax2.set_title('B', fontsize=16, fontweight='bold', loc='left')
    
    # 添加R-peak标记
    ax2.annotate('R-peak', xy=(0, Y_AXIS_RANGE[1]-0.05), xytext=(0, Y_AXIS_RANGE[1]+0.1),
                arrowprops=dict(arrowstyle='->', color='black', lw=1),
                ha='center', fontsize=10)
    
    # 右侧添加多个条件对比的脑地形图
    try:
        if 'HCT' in condition_data and 'C-TCT' in condition_data:
            # HCT - C-TCT
            ax_topo2 = fig.add_subplot(gs[1, 1])
            hct_data = load_hep_data(os.path.join(DATA_DIR, 'rest_01_hep_data.h5'))
            ctct_data = load_hep_data(os.path.join(DATA_DIR, 'test_01_hep_data.h5'))
            
            if hct_data and ctct_data and available_channels:
                hct_window = np.mean(hct_data['data'][:, ch_indices, t_start_idx:t_end_idx], axis=(0, 2)) * 1e6
                ctct_window = np.mean(ctct_data['data'][:, ch_indices, t_start_idx:t_end_idx], axis=(0, 2)) * 1e6
                diff_topo2 = hct_window - ctct_window
                
                im2, _ = plot_topomap(diff_topo2, info, axes=ax_topo2, show=False,
                                    cmap='RdBu_r', vlim=(-1, 1), contours=6)
                ax_topo2.set_title('HCT - C-TCT\nμV', fontsize=10)
        
        if 'HCT' in condition_data and 'BCT' in condition_data:
            # HCT - BCT
            ax_topo3 = fig.add_subplot(gs[1, 2])
            hct_data = load_hep_data(os.path.join(DATA_DIR, 'rest_01_hep_data.h5'))
            bct_data = load_hep_data(os.path.join(DATA_DIR, 'rest_02_hep_data.h5'))
            
            if hct_data and bct_data and available_channels:
                hct_window = np.mean(hct_data['data'][:, ch_indices, t_start_idx:t_end_idx], axis=(0, 2)) * 1e6
                bct_window = np.mean(bct_data['data'][:, ch_indices, t_start_idx:t_end_idx], axis=(0, 2)) * 1e6
                diff_topo3 = hct_window - bct_window
                
                im3, _ = plot_topomap(diff_topo3, info, axes=ax_topo3, show=False,
                                    cmap='RdBu_r', vlim=(-1, 1), contours=6)
                ax_topo3.set_title('HCT - BCT\nμV', fontsize=10)
        
        # 添加统一的颜色条
        if 'im1' in locals():
            cbar_ax = fig.add_subplot(gs[:, 3])
            cbar = plt.colorbar(im1, cax=cbar_ax)
            cbar.set_label('μV', fontsize=10)
            
    except Exception as e:
        print(f"绘制脑地形图时出错: {e}")
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图片
    output_path = os.path.join(OUTPUT_DIR, 'reference_style_hep_analysis.png')
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"参考样式HEP分析图已保存: {output_path}")
    
    return condition_data

def main():
    """主函数"""
    print("=" * 70)
    print("参考图片样式的HEP分析")
    print("完全复刻参考图片的视觉效果")
    print("=" * 70)
    
    try:
        # 创建参考样式的图
        condition_data = create_reference_style_plot()
        
        print(f"\n✅ 参考样式HEP分析完成！")
        print(f"   结果保存目录: {OUTPUT_DIR}")
        
        if condition_data:
            print(f"   成功处理的条件: {list(condition_data.keys())}")
        
    except Exception as e:
        print(f"❌ 分析过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
