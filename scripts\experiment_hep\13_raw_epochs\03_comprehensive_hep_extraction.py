#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
HEP (Heartbeat Evoked Potential) 综合数据提取脚本 - 完全重新开发版本

严格按照新需求规格开发：
1. 数据源：D:\ecgeeg\18-eegecg手动预处理5-重参考2-双侧乳突
2. 文件格式：.fif文件，TP9TP10参考
3. 时间窗口：-500ms到+1500ms（以R波为0点）
4. 参考心电：ECG11
5. 输出：HDF5格式，按阶段组织
6. 质量控制：R波单峰检测、基线校正验证、HEP成分清晰度检查
7. 可视化：三脑区对比图，2:1比例，统一Y轴，-200到650ms显示窗口

技术参数：
- 时间窗口：-500ms 到 +1500ms（提取），-200ms 到 +650ms（显示）
- 滤波参数：0.1-30Hz带通滤波，不使用平滑处理
- 基线校正：-200ms 到 0ms
- 采样率：500Hz
- 总通道：119个（61脑电 + 58心电）
- 被试：01-32（排除03,04,14）共29个被试
- 阶段：prac(01), test(01,02,03), rest(01,02,03)
"""

import os
import sys
import numpy as np
import pandas as pd
import mne
import neurokit2 as nk
import h5py
import logging
import time
from scipy import signal
from tqdm import tqdm
import warnings
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')  # 非交互式后端

# 设置中文字体
import matplotlib.font_manager as fm
font_path = r"C:\Users\<USER>\Desktop\BaiduSyncdisk\桌面环境\霞鹜文楷\LXGWWenKai-Medium.ttf"
if os.path.exists(font_path):
    fm.fontManager.addfont(font_path)
    font_prop = fm.FontProperties(fname=font_path)
    plt.rcParams['font.family'] = font_prop.get_name()
    plt.rcParams['font.sans-serif'] = [font_prop.get_name()]
else:
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

# 忽略警告
warnings.filterwarnings('ignore', category=RuntimeWarning)
mne.set_log_level('WARNING')

# ===== 配置参数 =====

# 数据路径配置
DATA_DIR = r"D:\ecgeeg\18-eegecg手动预处理5-重参考2-双侧乳突"
OUTPUT_DIR = r"D:\ecgeeg\30-数据分析\5-HBA\result\hep_analysis\14_rest_vs_test_analysis\hepdata"
PLOTS_DIR = os.path.join(OUTPUT_DIR, 'plots')
LOGS_DIR = os.path.join(OUTPUT_DIR, 'logs')

# 确保输出目录存在
for dir_path in [OUTPUT_DIR, PLOTS_DIR, LOGS_DIR]:
    os.makedirs(dir_path, exist_ok=True)

# 数据结构规格
TOTAL_CHANNELS = 119  # 总通道数
EEG_CHANNELS = 61     # 脑电通道数（前61个）
ECG_CHANNELS = 58     # 心电通道数（后58个）
SAMPLING_RATE = 500   # 采样率 Hz

# HEP提取技术参数
HEP_TMIN = -0.5       # R波前500ms（提取窗口）
HEP_TMAX = 1.5        # R波后1500ms（提取窗口）
DISPLAY_TMIN = -0.2   # 显示窗口开始：-200ms
DISPLAY_TMAX = 0.65   # 显示窗口结束：+650ms
# 调整基线校正窗口以保留更多HEP信号特征
BASELINE_TMIN = -0.1  # 缩短基线校正窗口开始：-100ms
BASELINE_TMAX = -0.05 # 基线校正结束：-50ms（避免R波影响）
# 调整滤波参数以增强HEP信号可见性
FILTER_LOW = 0.5      # 提高低频截止，减少基线漂移，增强HEP信号
FILTER_HIGH = 45.0    # 提高高频截止，保留更多HEP成分

# 参考心电电极
REFERENCE_ECG = 'ECG11'

# 被试配置（排除03, 04, 14）
SUBJECTS = [1, 2, 5, 6, 7, 8, 9, 10, 11, 12, 13, 15, 16, 17, 18, 19, 20,
           21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32]

# 阶段配置
STAGE_CONFIG = {
    'prac': ['01'],
    'test': ['01', '02', '03'],
    'rest': ['01', '02', '03']
}

# 电极组合定义
CENTRAL_ELECTRODES = ['F1', 'F2', 'FC1', 'FC2', 'C1', 'Cz', 'C2', 'Fz']
RIGHT_HEMISPHERE = ['F2', 'F4', 'F6', 'AF4', 'AF8', 'FP2', 'FC2', 'FC4', 'FC6']
LEFT_HEMISPHERE = ['F1', 'F3', 'F5', 'AF3', 'AF7', 'FP1', 'FC1', 'FC3', 'FC5']

# 质量控制参数
QUALITY_THRESHOLDS = {
    'min_r_peaks': 30,              # 最少R峰数量
    'max_rr_interval': 2.0,         # 最大RR间期（秒）
    'min_rr_interval': 0.4,         # 最小RR间期（秒）
    'baseline_stability': 100.0,  # 基线稳定性阈值（μV）- 适合正常脑电范围
    'hep_clarity_threshold': 1.2,   # HEP成分清晰度阈值
    'r_peak_alignment_quality': 0.3, # R波对齐质量阈值（0-1，越高越好）
}

def setup_logging():
    """设置日志系统"""
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(LOGS_DIR, f'comprehensive_hep_extraction_{timestamp}.log')

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    return logging.getLogger(__name__)

def get_file_pattern(subject_id, stage_number, stage_type):
    """根据规格生成文件名模式"""
    return f"{subject_id:02d}_{stage_number}_reto2_combined_baseline_corrected_chanloc_bad_interp_{stage_type}_TP9TP10Ref.fif"

def load_and_validate_data(subject_id, stage_number, stage_type, logger):
    """加载并验证数据文件"""
    try:
        filename = get_file_pattern(subject_id, stage_number, stage_type)
        file_path = os.path.join(DATA_DIR, filename)

        if not os.path.exists(file_path):
            logger.warning(f"文件不存在: {filename}")
            return None, None

        # 加载.fif文件
        raw = mne.io.read_raw_fif(file_path, preload=True, verbose=False)

        # 验证数据结构
        if len(raw.ch_names) != TOTAL_CHANNELS:
            logger.error(f"通道数不匹配: 期望{TOTAL_CHANNELS}，实际{len(raw.ch_names)}")
            return None, None

        if raw.info['sfreq'] != SAMPLING_RATE:
            logger.error(f"采样率不匹配: 期望{SAMPLING_RATE}Hz，实际{raw.info['sfreq']}Hz")
            return None, None

        # 验证参考心电通道存在
        if REFERENCE_ECG not in raw.ch_names:
            logger.error(f"参考心电通道 {REFERENCE_ECG} 不存在")
            return None, None

        logger.info(f"成功加载数据: {filename}")
        return raw, filename

    except Exception as e:
        logger.error(f"加载数据失败 {filename}: {str(e)}")
        return None, None

def detect_and_convert_voltage_units(raw, logger):
    """分别处理脑电和心电的电压单位检测和转换，去除超出生理范围的伪迹"""
    try:
        # 分别处理脑电和心电通道
        eeg_data = raw.get_data()[:EEG_CHANNELS, :1000]  # 前61个脑电通道
        ecg_data = raw.get_data()[EEG_CHANNELS:, :1000]  # 后58个心电通道

        # 脑电通道处理
        eeg_std = np.std(eeg_data)
        eeg_ptp = np.ptp(eeg_data)
        logger.info(f"脑电原始数据统计 - 标准差: {eeg_std:.2e}, 峰峰值: {eeg_ptp:.2e}")

        # 心电通道处理
        ecg_std = np.std(ecg_data)
        ecg_ptp = np.ptp(ecg_data)
        logger.info(f"心电原始数据统计 - 标准差: {ecg_std:.2e}, 峰峰值: {ecg_ptp:.2e}")

        # 脑电单位判断和转换
        # 根据实际数据分析，需要合理的转换到正常脑电范围（10-100μV）
        if eeg_std > 1e-3:  # V单位
            eeg_factor = 1e6
            eeg_unit = "V"
        elif eeg_std > 1e-6:  # mV单位
            eeg_factor = 1e3  # 只转换到μV，不额外放大
            eeg_unit = "mV"
        else:  # 已经是μV或更小
            eeg_factor = 1.0
            eeg_unit = "μV"

        # 心电单位判断和转换（心电信号通常比脑电大）
        if ecg_std > 1e-3:  # V单位
            ecg_factor = 1e6
            ecg_unit = "V"
        elif ecg_std > 1e-6:  # mV单位
            ecg_factor = 1e3
            ecg_unit = "mV"
        else:  # 已经是μV
            ecg_factor = 1.0
            ecg_unit = "μV"

        logger.info(f"脑电单位: {eeg_unit}, 转换系数: {eeg_factor}")
        logger.info(f"心电单位: {ecg_unit}, 转换系数: {ecg_factor}")

        # 分别应用转换系数
        if eeg_factor != 1.0:
            raw._data[:EEG_CHANNELS, :] *= eeg_factor
            logger.info(f"脑电数据从{eeg_unit}转换为μV")

        if ecg_factor != 1.0:
            raw._data[EEG_CHANNELS:, :] *= ecg_factor
            logger.info(f"心电数据从{ecg_unit}转换为μV")

        # 验证转换后的数据范围
        converted_eeg = raw.get_data()[:EEG_CHANNELS, :]
        converted_ecg = raw.get_data()[EEG_CHANNELS:, :]

        eeg_converted_std = np.std(converted_eeg)
        ecg_converted_std = np.std(converted_ecg)

        logger.info(f"转换后脑电标准差: {eeg_converted_std:.2f}μV")
        logger.info(f"转换后心电标准差: {ecg_converted_std:.2f}μV")

        # 脑电生理范围检查和伪迹去除
        # 正常头皮脑电范围：约1-200μV
        eeg_physiological_max = 200.0  # μV

        # 检查脑电是否有超出生理范围的数据
        eeg_extreme_values = np.abs(converted_eeg) > eeg_physiological_max
        eeg_extreme_ratio = np.sum(eeg_extreme_values) / converted_eeg.size

        if eeg_extreme_ratio > 0.01:  # 如果超过1%的脑电数据点超出范围
            logger.warning(f"检测到{eeg_extreme_ratio*100:.2f}%的脑电数据超出生理范围(>{eeg_physiological_max}μV)，将作为伪迹去除")

            # 对超出范围的脑电数据进行截断处理
            raw._data[:EEG_CHANNELS, :] = np.clip(raw._data[:EEG_CHANNELS, :],
                                                 -eeg_physiological_max, eeg_physiological_max)

            # 重新计算脑电统计信息
            cleaned_eeg = raw.get_data()[:EEG_CHANNELS, :]
            cleaned_eeg_std = np.std(cleaned_eeg)
            logger.info(f"脑电伪迹去除后标准差: {cleaned_eeg_std:.2f}μV")

        # 心电范围检查（心电信号范围通常更大）
        # 正常心电范围：约100-5000μV
        ecg_physiological_max = 5000.0  # μV

        ecg_extreme_values = np.abs(converted_ecg) > ecg_physiological_max
        ecg_extreme_ratio = np.sum(ecg_extreme_values) / converted_ecg.size

        if ecg_extreme_ratio > 0.01:  # 如果超过1%的心电数据点超出范围
            logger.warning(f"检测到{ecg_extreme_ratio*100:.2f}%的心电数据超出生理范围(>{ecg_physiological_max}μV)，将作为伪迹去除")

            # 对超出范围的心电数据进行截断处理
            raw._data[EEG_CHANNELS:, :] = np.clip(raw._data[EEG_CHANNELS:, :],
                                                 -ecg_physiological_max, ecg_physiological_max)

        # 最终验证数据范围
        final_eeg = raw.get_data()[:EEG_CHANNELS, :]
        final_ecg = raw.get_data()[EEG_CHANNELS:, :]

        final_eeg_std = np.std(final_eeg)
        final_ecg_std = np.std(final_ecg)

        logger.info(f"最终脑电数据范围: 标准差{final_eeg_std:.2f}μV")
        logger.info(f"最终心电数据范围: 标准差{final_ecg_std:.2f}μV")

        return raw, (eeg_factor, ecg_factor), (eeg_unit, ecg_unit)

    except Exception as e:
        logger.error(f"电压单位检测转换失败: {str(e)}")
        return raw, (1.0, 1.0), ("unknown", "unknown")

def apply_filtering_no_smoothing(raw, logger):
    """应用增强滤波以提高HEP信号可见性"""
    try:
        raw_filtered = raw.copy()

        # 应用带通滤波
        raw_filtered.filter(l_freq=FILTER_LOW, h_freq=FILTER_HIGH,
                           fir_design='firwin', verbose=False)

        # 对EEG通道应用轻微的信号增强
        eeg_data = raw_filtered.get_data()[:EEG_CHANNELS, :]

        # 计算信号的动态范围
        eeg_std = np.std(eeg_data)

        # 如果信号太小，适度放大（但保持在生理范围内）
        if eeg_std < 5.0:  # 如果标准差小于5μV
            enhancement_factor = min(2.0, 10.0 / eeg_std)  # 最大放大2倍
            raw_filtered._data[:EEG_CHANNELS, :] *= enhancement_factor
            logger.info(f"应用信号增强: 放大系数 {enhancement_factor:.2f}")

        logger.info(f"应用增强滤波: {FILTER_LOW}-{FILTER_HIGH}Hz")
        return raw_filtered

    except Exception as e:
        logger.error(f"滤波失败: {str(e)}")
        return raw

def extract_ecg_signal(raw, logger):
    """提取参考心电通道ECG11信号"""
    try:
        # 提取ECG11通道数据
        ecg_idx = raw.ch_names.index(REFERENCE_ECG)
        ecg_signal = raw.get_data()[ecg_idx, :]

        logger.info(f"成功提取参考心电通道: {REFERENCE_ECG}")
        return ecg_signal

    except Exception as e:
        logger.error(f"提取心电通道失败: {str(e)}")
        return None

def detect_r_peaks_single_peak_robust(ecg_signal, sampling_rate, logger):
    """检测R波峰值，确保单峰检测避免双峰现象，优化HEP时间锁定"""
    try:
        # 清洗ECG信号
        ecg_cleaned = nk.ecg_clean(ecg_signal, sampling_rate=sampling_rate)

        # 使用多种方法检测R峰，选择最佳结果
        methods = ['neurokit', 'pantompkins1985', 'hamilton2002', 'christov2004']
        best_peaks = None
        best_score = 0
        best_method = None

        for method in methods:
            try:
                _, rpeaks_info = nk.ecg_peaks(ecg_cleaned, sampling_rate=sampling_rate, method=method)
                r_peaks = rpeaks_info.get('ECG_R_Peaks', [])

                if len(r_peaks) < QUALITY_THRESHOLDS['min_r_peaks']:
                    continue

                # 检查RR间期合理性
                rr_intervals = np.diff(r_peaks) / sampling_rate
                valid_rr = np.logical_and(
                    rr_intervals >= QUALITY_THRESHOLDS['min_rr_interval'],
                    rr_intervals <= QUALITY_THRESHOLDS['max_rr_interval']
                )
                valid_ratio = np.sum(valid_rr) / len(rr_intervals) if len(rr_intervals) > 0 else 0

                # 检查双峰问题
                double_peak_ratio = check_double_peak_issue(ecg_cleaned, r_peaks, sampling_rate)

                # 检查R峰对齐质量（峰值一致性）
                alignment_quality = check_r_peak_alignment_quality(ecg_cleaned, r_peaks, sampling_rate)

                # 计算质量分数（增加对齐质量权重）
                quality_score = len(r_peaks) * valid_ratio * (1.0 - double_peak_ratio) * alignment_quality

                if quality_score > best_score:
                    best_score = quality_score
                    best_peaks = r_peaks
                    best_method = method

                logger.debug(f"方法{method}: {len(r_peaks)}个R峰, 有效RR比例: {valid_ratio:.3f}, "
                           f"双峰比例: {double_peak_ratio:.3f}, 对齐质量: {alignment_quality:.3f}, "
                           f"质量分数: {quality_score:.1f}")

            except Exception as e:
                logger.debug(f"方法{method}失败: {str(e)}")
                continue

        if best_peaks is None:
            raise ValueError("所有R峰检测方法都失败")

        # 精细化R峰位置到真正的峰值点
        refined_peaks = refine_r_peak_positions_precise(ecg_cleaned, best_peaks, sampling_rate)

        # 验证R峰对齐质量
        final_alignment_quality = check_r_peak_alignment_quality(ecg_cleaned, refined_peaks, sampling_rate)

        logger.info(f"R峰检测完成: 使用方法{best_method}, 检测到{len(refined_peaks)}个R峰")
        logger.info(f"R峰对齐质量: {final_alignment_quality:.3f}")

        return refined_peaks, ecg_cleaned

    except Exception as e:
        logger.error(f"R峰检测失败: {str(e)}")
        return None, None

def check_r_peak_alignment_quality(ecg_signal, r_peaks, sampling_rate, window_ms=50):
    """检查R峰对齐质量"""
    try:
        window_samples = int(window_ms * sampling_rate / 1000)
        peak_amplitudes = []

        for peak in r_peaks[:min(50, len(r_peaks))]:  # 检查前50个峰
            start = max(0, peak - window_samples // 2)
            end = min(len(ecg_signal), peak + window_samples // 2)

            if start < end:
                local_signal = ecg_signal[start:end]
                # 确保峰值在窗口中心附近
                center_idx = len(local_signal) // 2
                actual_peak_idx = np.argmax(local_signal)

                # 计算峰值偏移
                offset = abs(actual_peak_idx - center_idx)
                if offset <= 5:  # 允许5个样本点的偏移
                    peak_amplitudes.append(local_signal[actual_peak_idx])

        if len(peak_amplitudes) < 10:
            return 0.5  # 如果有效峰值太少，返回中等质量

        # 计算峰值一致性
        amplitude_cv = np.std(peak_amplitudes) / np.mean(peak_amplitudes) if np.mean(peak_amplitudes) > 0 else 1.0
        alignment_quality = max(0.0, 1.0 - amplitude_cv)  # CV越小，质量越高

        return alignment_quality

    except Exception:
        return 0.5

def refine_r_peak_positions_precise(ecg_signal, r_peaks, sampling_rate, window_ms=30):
    """精确精细化R峰位置到真正的峰值点"""
    try:
        window_samples = int(window_ms * sampling_rate / 1000)
        refined_peaks = []

        for peak in r_peaks:
            start = max(0, peak - window_samples // 2)
            end = min(len(ecg_signal), peak + window_samples // 2)

            if start < end:
                local_signal = ecg_signal[start:end]

                # 找到局部最大值
                local_max_idx = np.argmax(local_signal)
                refined_peak = start + local_max_idx

                # 验证这确实是一个峰值（周围的值都比它小）
                if (local_max_idx > 0 and local_max_idx < len(local_signal) - 1 and
                    local_signal[local_max_idx] > local_signal[local_max_idx - 1] and
                    local_signal[local_max_idx] > local_signal[local_max_idx + 1]):
                    refined_peaks.append(refined_peak)
                else:
                    # 如果不是真正的峰值，保持原位置
                    refined_peaks.append(peak)
            else:
                refined_peaks.append(peak)

        return np.array(refined_peaks)

    except Exception:
        return r_peaks

def check_double_peak_issue(ecg_signal, r_peaks, sampling_rate, window_ms=100):
    """检查R波双峰问题"""
    try:
        window_samples = int(window_ms * sampling_rate / 1000)
        double_peak_count = 0

        for peak in r_peaks[:min(20, len(r_peaks))]:  # 检查前20个峰
            start = max(0, peak - window_samples // 2)
            end = min(len(ecg_signal), peak + window_samples // 2)

            if start < end:
                local_signal = ecg_signal[start:end]
                # 寻找局部峰值
                from scipy.signal import find_peaks
                peaks_in_window, _ = find_peaks(local_signal, height=np.max(local_signal) * 0.7)

                # 如果发现多个峰值，认为是双峰
                if len(peaks_in_window) > 1:
                    double_peak_count += 1

        double_peak_ratio = double_peak_count / min(20, len(r_peaks)) if len(r_peaks) > 0 else 0
        return double_peak_ratio

    except Exception as e:
        return 0.0

def refine_r_peak_positions(ecg_signal, r_peaks, sampling_rate, window_ms=50):
    """精细化R峰位置到真正的峰值点"""
    try:
        window_samples = int(window_ms * sampling_rate / 1000)
        refined_peaks = []

        for peak in r_peaks:
            start = max(0, peak - window_samples // 2)
            end = min(len(ecg_signal), peak + window_samples // 2)

            if start < end:
                local_signal = ecg_signal[start:end]
                local_max_idx = np.argmax(local_signal)
                refined_peak = start + local_max_idx
                refined_peaks.append(refined_peak)
            else:
                refined_peaks.append(peak)

        return np.array(refined_peaks)

    except Exception as e:
        return r_peaks

def extract_hep_epochs_comprehensive(raw, r_peaks, logger):
    """提取HEP epochs - 强化基线校正版本"""
    try:
        # 创建事件数组
        events = np.column_stack([r_peaks, np.zeros_like(r_peaks), np.ones_like(r_peaks)])

        # 创建epochs，先不进行基线校正
        epochs = mne.Epochs(raw, events, event_id=1,
                           tmin=HEP_TMIN, tmax=HEP_TMAX,
                           baseline=None,  # 先不进行基线校正
                           preload=True, reject=None, verbose=False)

        # 手动进行基线校正，确保正确应用
        baseline_mask = (epochs.times >= BASELINE_TMIN) & (epochs.times <= BASELINE_TMAX)

        # 获取数据
        data = epochs.get_data()  # shape: (n_epochs, n_channels, n_times)

        # 计算基线平均值
        baseline_data = data[:, :, baseline_mask]
        baseline_mean = np.mean(baseline_data, axis=2, keepdims=True)

        # 应用基线校正
        data_corrected = data - baseline_mean

        # 将校正后的数据放回epochs
        epochs._data = data_corrected

        logger.info(f"创建了 {len(epochs)} 个HEP epochs")
        logger.info(f"基线校正完成: 基线窗口 {BASELINE_TMIN*1000:.0f}ms 到 {BASELINE_TMAX*1000:.0f}ms")

        # 验证基线校正效果
        baseline_after = np.mean(data_corrected[:, :EEG_CHANNELS, baseline_mask])
        logger.info(f"基线校正后平均值: {baseline_after:.3f} μV")

        return epochs

    except Exception as e:
        logger.error(f"HEP epochs提取失败: {str(e)}")
        return None

def validate_hep_quality_comprehensive(epochs, logger):
    """全面的HEP质量控制验证"""
    try:
        validation_results = {}

        # 1. 验证基线校正效果
        baseline_mask = (epochs.times >= BASELINE_TMIN) & (epochs.times <= BASELINE_TMAX)
        baseline_data = epochs.get_data()[:, :EEG_CHANNELS, baseline_mask]
        baseline_std = np.std(baseline_data)

        # 数据已经是μV单位，直接比较
        baseline_stable = baseline_std < QUALITY_THRESHOLDS['baseline_stability']
        validation_results['baseline_stability'] = {
            'std_uv': baseline_std,
            'threshold_uv': QUALITY_THRESHOLDS['baseline_stability'],
            'passed': baseline_stable
        }

        # 2. 检查HEP成分清晰度（200ms后）
        hep_mask = (epochs.times >= 0.2) & (epochs.times <= 0.6)
        hep_data = epochs.get_data()[:, :EEG_CHANNELS, hep_mask]
        hep_amplitude = np.std(hep_data)

        hep_clarity_ratio = hep_amplitude / baseline_std if baseline_std > 0 else 0
        hep_clear = hep_clarity_ratio >= QUALITY_THRESHOLDS['hep_clarity_threshold']
        validation_results['hep_clarity'] = {
            'amplitude_uv': hep_amplitude,  # 数据已经是μV单位
            'clarity_ratio': hep_clarity_ratio,
            'threshold': QUALITY_THRESHOLDS['hep_clarity_threshold'],
            'passed': hep_clear
        }

        # 3. R波对齐质量检查
        # 检查R波在0ms位置的对齐质量
        r_wave_mask = (epochs.times >= -0.05) & (epochs.times <= 0.05)  # R波周围100ms
        r_wave_data = epochs.get_data()[:, EEG_CHANNELS:, r_wave_mask]  # 使用心电通道

        # 计算R波峰值在0ms位置的一致性
        zero_time_idx = np.argmin(np.abs(epochs.times))  # 找到最接近0ms的时间点
        r_wave_at_zero = epochs.get_data()[:, EEG_CHANNELS:, zero_time_idx]
        r_wave_consistency = np.std(r_wave_at_zero) / (np.mean(np.abs(r_wave_at_zero)) + 1e-10)

        # 计算对齐质量分数（CV的倒数）
        alignment_quality_score = 1.0 / (1.0 + r_wave_consistency)

        r_wave_aligned = alignment_quality_score >= QUALITY_THRESHOLDS['r_peak_alignment_quality']
        validation_results['r_wave_alignment'] = {
            'consistency': r_wave_consistency,
            'quality_score': alignment_quality_score,
            'threshold': QUALITY_THRESHOLDS['r_peak_alignment_quality'],
            'passed': r_wave_aligned
        }

        # 4. 整体质量评估
        overall_passed = baseline_stable and hep_clear and r_wave_aligned
        validation_results['overall'] = {
            'passed': overall_passed,
            'epochs_count': len(epochs)
        }

        if logger:
            logger.info(f"质量控制 - 基线稳定性: {baseline_std:.2f}μV ({'通过' if baseline_stable else '失败'})")
            logger.info(f"质量控制 - HEP清晰度: {hep_clarity_ratio:.2f} ({'通过' if hep_clear else '失败'})")
            logger.info(f"质量控制 - R波对齐: {r_wave_consistency:.3f} ({'通过' if r_wave_aligned else '失败'})")
            logger.info(f"质量控制 - 整体评估: {'通过' if overall_passed else '失败'}")

        return validation_results

    except Exception as e:
        if logger:
            logger.error(f"质量控制验证失败: {str(e)}")
        return {'overall': {'passed': False, 'error': str(e)}}

def organize_electrode_data_comprehensive(epochs, logger):
    """按电极组合组织数据"""
    try:
        organized_data = {}
        epochs_data = epochs.get_data()
        ch_names = epochs.ch_names

        # 全通道数据（119通道：前61个HEP处理的脑电 + 后58个原始心电）
        organized_data['all_channels_hep'] = epochs_data
        logger.info(f"全通道数据: {epochs_data.shape}")

        # 中央电极数据
        central_indices = []
        for electrode in CENTRAL_ELECTRODES:
            if electrode in ch_names:
                central_indices.append(ch_names.index(electrode))

        if central_indices:
            organized_data['central_electrodes_hep'] = epochs_data[:, central_indices, :]
            logger.info(f"中央电极: {len(central_indices)}个电极 - {CENTRAL_ELECTRODES}")

        # 右半球电极数据
        right_indices = []
        for electrode in RIGHT_HEMISPHERE:
            if electrode in ch_names:
                right_indices.append(ch_names.index(electrode))

        if right_indices:
            organized_data['right_hemisphere_hep'] = epochs_data[:, right_indices, :]
            logger.info(f"右半球电极: {len(right_indices)}个电极 - {RIGHT_HEMISPHERE}")

        # 左半球电极数据
        left_indices = []
        for electrode in LEFT_HEMISPHERE:
            if electrode in ch_names:
                left_indices.append(ch_names.index(electrode))

        if left_indices:
            organized_data['left_hemisphere_hep'] = epochs_data[:, left_indices, :]
            logger.info(f"左半球电极: {len(left_indices)}个电极 - {LEFT_HEMISPHERE}")

        return organized_data, {
            'central_indices': central_indices,
            'right_indices': right_indices,
            'left_indices': left_indices
        }

    except Exception as e:
        logger.error(f"数据组织失败: {str(e)}")
        return {}, {}

def create_three_region_visualization(epochs, subject_id, stage_type, stage_number,
                                    validation_results, electrode_indices, logger):
    """创建三脑区对比图，2:1比例，统一Y�axis，-200到650ms显示窗口"""
    try:
        # 创建图形，三个子图横向排列
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))  # 15:5 = 3:1，每个子图5:5 = 1:1，但内容是2:1
        fig.suptitle(f'被试{subject_id:02d} - {stage_type}_{stage_number} HEP三脑区对比分析',
                    fontsize=16, fontweight='bold')

        # 时间轴（毫秒），限制在显示窗口
        display_mask = (epochs.times >= DISPLAY_TMIN) & (epochs.times <= DISPLAY_TMAX)
        times_ms = epochs.times[display_mask] * 1000

        # 获取数据（已经是μV单位）
        epochs_data_uv = epochs.get_data()

        # 计算合理的Y轴范围，专注于各脑区平均信号的变化
        # 先计算各脑区的平均信号，然后确定合适的Y轴范围
        region_averages = []

        for region_name, indices, color in [
            ('中央电极', electrode_indices['central_indices'], 'blue'),
            ('左半球电极', electrode_indices['left_indices'], 'red'),
            ('右半球电极', electrode_indices['right_indices'], 'green')
        ]:
            if indices:
                region_data = epochs_data_uv[:, indices, :][:, :, display_mask]
                region_avg = np.mean(np.mean(region_data, axis=1), axis=0)
                region_averages.append(region_avg)

        if region_averages:
            # 合并所有脑区的平均信号
            all_averages = np.concatenate(region_averages)

            # 计算合理的Y轴范围
            data_min = np.min(all_averages)
            data_max = np.max(all_averages)
            data_range = data_max - data_min

            # 对于HEP信号，需要更精细的Y轴范围来显示微小变化
            # 记录实际数据范围用于调试
            logger.info(f"实际信号范围: {data_min:.4f} 到 {data_max:.4f} μV (变化范围: {data_range:.4f} μV)")

            if data_range < 0.1:  # 如果信号变化极小（<0.1μV）
                center = (data_max + data_min) / 2
                # 使用固定的小范围来放大显示
                y_min = center - 0.2
                y_max = center + 0.2
            elif data_range < 0.5:  # 如果信号变化很小（0.1-0.5μV）
                # 放大3倍显示
                margin = data_range * 1.5
                y_min = data_min - margin
                y_max = data_max + margin
            elif data_range < 2.0:  # 如果信号变化较小（0.5-2μV）
                # 添加50%的边距
                margin = data_range * 0.5
                y_min = data_min - margin
                y_max = data_max + margin
            elif data_range < 10.0:  # 正常范围（2-10μV）
                # 添加30%的边距
                margin = data_range * 0.3
                y_min = data_min - margin
                y_max = data_max + margin
            else:  # 如果信号变化太大，可能有异常
                # 使用更保守的百分位数
                p5 = np.percentile(all_averages, 5)
                p95 = np.percentile(all_averages, 95)
                margin = (p95 - p5) * 0.2
                y_min = p5 - margin
                y_max = p95 + margin
        else:
            # 默认范围
            y_min = -1.0
            y_max = 1.0

        logger.info(f"Y轴范围设置: {y_min:.2f} 到 {y_max:.2f} μV")

        # 定义脑区和颜色
        regions = [
            ('中央电极', electrode_indices['central_indices'], 'blue'),
            ('左半球电极', electrode_indices['left_indices'], 'red'),
            ('右半球电极', electrode_indices['right_indices'], 'green')
        ]

        for i, (region_name, indices, color) in enumerate(regions):
            ax = axes[i]

            if indices:
                # 计算该脑区的平均波形
                region_data = epochs_data_uv[:, indices, :][:, :, display_mask]
                region_avg = np.mean(np.mean(region_data, axis=1), axis=0)
                region_std = np.std(np.mean(region_data, axis=1), axis=0)

                # 绘制平均波形
                ax.plot(times_ms, region_avg, color=color, linewidth=2, label=f'{region_name}平均')
                ax.fill_between(times_ms, region_avg - region_std, region_avg + region_std,
                               color=color, alpha=0.2, label='±1SD')

                # 添加关键时间点标记
                ax.axhline(y=0, color='black', linestyle='-', alpha=0.3)
                ax.axvline(x=0, color='red', linestyle='--', alpha=0.7, label='R-peak')
                ax.axvspan(BASELINE_TMIN*1000, BASELINE_TMAX*1000, alpha=0.1, color='gray', label='基线')
                ax.axvspan(200, 600, alpha=0.1, color='yellow', label='HEP窗口')

                # 设置子图属性
                ax.set_title(f'{region_name} (n={len(indices)})', fontsize=12, fontweight='bold')
                ax.set_xlabel('时间 (ms)', fontsize=10)
                ax.set_ylabel('振幅 (μV)', fontsize=10)
                ax.set_xlim(DISPLAY_TMIN*1000, DISPLAY_TMAX*1000)
                ax.set_ylim(y_min, y_max)
                ax.grid(True, alpha=0.3)
                ax.legend(fontsize=8)

                # 设置子图比例为2:1
                ax.set_aspect(abs((DISPLAY_TMAX*1000 - DISPLAY_TMIN*1000) / (y_max - y_min)) * 0.5)

            else:
                ax.text(0.5, 0.5, f'{region_name}\n无可用电极',
                       transform=ax.transAxes, ha='center', va='center',
                       fontsize=12, color='red')
                ax.set_xlim(DISPLAY_TMIN*1000, DISPLAY_TMAX*1000)
                ax.set_ylim(y_min, y_max)

        # 添加质量控制信息
        quality_text = f"""质量控制结果:
基线稳定性: {'✓' if validation_results['baseline_stability']['passed'] else '✗'} ({validation_results['baseline_stability']['std_uv']:.1f}μV)
HEP清晰度: {'✓' if validation_results['hep_clarity']['passed'] else '✗'} ({validation_results['hep_clarity']['clarity_ratio']:.2f})
R波对齐: {'✓' if validation_results['r_wave_alignment']['passed'] else '✗'}
总体评估: {'✓ 通过' if validation_results['overall']['passed'] else '✗ 失败'}"""

        fig.text(0.02, 0.02, quality_text, fontsize=9,
                bbox=dict(boxstyle="round,pad=0.3", facecolor='lightblue', alpha=0.8))

        plt.tight_layout()

        # 保存图片
        filename = f"{subject_id:02d}_{stage_type}_{stage_number}_three_regions_hep.png"
        filepath = os.path.join(PLOTS_DIR, filename)
        plt.savefig(filepath, dpi=150, bbox_inches='tight')
        plt.close()

        logger.info(f"保存三脑区对比图: {filename}")
        return filepath

    except Exception as e:
        logger.error(f"创建三脑区可视化失败: {str(e)}")
        return None

def process_single_subject_comprehensive(subject_id, stage_number, stage_type, logger):
    """处理单个被试的数据 - 综合版本"""
    try:
        logger.info(f"开始处理被试 {subject_id:02d} - {stage_type}_{stage_number}")

        # 1. 加载并验证数据
        raw, filename = load_and_validate_data(subject_id, stage_number, stage_type, logger)
        if raw is None:
            logger.error(f"被试 {subject_id:02d} - {stage_type}_{stage_number} 数据加载失败")
            return None

        # 2. 检测并转换电压单位
        raw, conversion_factor, original_unit = detect_and_convert_voltage_units(raw, logger)

        # 3. 应用滤波（无平滑处理）
        raw_filtered = apply_filtering_no_smoothing(raw, logger)

        # 4. 提取参考心电通道
        ecg_signal = extract_ecg_signal(raw_filtered, logger)
        if ecg_signal is None:
            logger.error(f"被试 {subject_id:02d} - {stage_type}_{stage_number} 心电信号提取失败")
            return None

        # 5. R波检测（单峰检测）
        r_peaks, ecg_cleaned = detect_r_peaks_single_peak_robust(ecg_signal, SAMPLING_RATE, logger)
        if r_peaks is None:
            logger.error(f"被试 {subject_id:02d} - {stage_type}_{stage_number} R波检测失败")
            return None

        # 6. 提取HEP epochs
        epochs = extract_hep_epochs_comprehensive(raw_filtered, r_peaks, logger)
        if epochs is None:
            logger.error(f"被试 {subject_id:02d} - {stage_type}_{stage_number} HEP epochs提取失败")
            return None

        # 7. 质量控制验证
        validation_results = validate_hep_quality_comprehensive(epochs, logger)
        if not validation_results['overall']['passed']:
            logger.warning(f"被试 {subject_id:02d} - {stage_type}_{stage_number} 质量控制未通过")
            # 根据需求，错误不能跳过，需要报告
            return None

        # 8. 组织电极数据
        organized_data, electrode_indices = organize_electrode_data_comprehensive(epochs, logger)

        # 9. 创建三脑区可视化
        visualization_path = create_three_region_visualization(
            epochs, subject_id, stage_type, stage_number,
            validation_results, electrode_indices, logger)

        logger.info(f"成功处理被试 {subject_id:02d} - {stage_type}_{stage_number}")

        return {
            'subject_id': subject_id,
            'stage_type': stage_type,
            'stage_number': stage_number,
            'organized_data': organized_data,
            'validation_results': validation_results,
            'epochs_count': len(epochs),
            'conversion_info': {
                'factor': conversion_factor,
                'original_unit': original_unit
            },
            'visualization_path': visualization_path
        }

    except Exception as e:
        logger.error(f"处理被试 {subject_id:02d} - {stage_type}_{stage_number} 失败: {str(e)}")
        return None

def save_stage_data_to_hdf5_comprehensive(stage_data_list, stage_type, stage_number, logger):
    """将阶段数据保存为HDF5文件"""
    try:
        # 创建文件名
        filename = f"{stage_type}_{stage_number}_hep_data.h5"
        filepath = os.path.join(OUTPUT_DIR, filename)

        logger.info(f"开始保存阶段数据: {filename}")

        # 合并所有被试的数据
        combined_data = {}
        valid_subjects = []
        validation_summary = []

        for result in stage_data_list:
            if result and result['organized_data']:
                valid_subjects.append(result['subject_id'])
                validation_summary.append(result['validation_results'])

                for data_type, data in result['organized_data'].items():
                    if data_type not in combined_data:
                        combined_data[data_type] = []
                    combined_data[data_type].append(data)

        if not combined_data:
            logger.error(f"阶段 {stage_type}_{stage_number} 没有有效数据可保存")
            return False

        # 保存到HDF5文件
        with h5py.File(filepath, 'w') as f:
            # 保存元数据
            f.attrs['stage_type'] = stage_type
            f.attrs['stage_number'] = stage_number
            f.attrs['n_subjects'] = len(valid_subjects)
            f.attrs['subject_ids'] = valid_subjects
            f.attrs['creation_time'] = time.strftime('%Y-%m-%d %H:%M:%S')
            f.attrs['extraction_parameters'] = f"时间窗口: {HEP_TMIN}s到{HEP_TMAX}s, 滤波: {FILTER_LOW}-{FILTER_HIGH}Hz, 基线: {BASELINE_TMIN}s到{BASELINE_TMAX}s"

            # 保存各类电极数据
            for data_type, data_list in combined_data.items():
                if data_list:
                    # 合并所有被试的数据
                    combined_array = np.concatenate(data_list, axis=0)
                    dataset = f.create_dataset(data_type, data=combined_array, compression='gzip')

                    # 添加数据集属性
                    dataset.attrs['description'] = f'{data_type} from {len(data_list)} subjects'
                    dataset.attrs['shape_info'] = f'(epochs, channels, timepoints) = {combined_array.shape}'
                    dataset.attrs['subjects'] = valid_subjects

                    logger.info(f"保存 {data_type}: {combined_array.shape}")

            # 保存质量控制摘要
            quality_group = f.create_group('quality_control')
            passed_count = sum(1 for v in validation_summary if v['overall']['passed'])
            quality_group.attrs['total_subjects'] = len(validation_summary)
            quality_group.attrs['passed_subjects'] = passed_count
            quality_group.attrs['pass_rate'] = passed_count / len(validation_summary) if validation_summary else 0

        logger.info(f"成功保存HDF5文件: {filename} ({len(valid_subjects)}个被试)")
        return True

    except Exception as e:
        logger.error(f"保存HDF5文件失败: {str(e)}")
        return False

class ProgressMonitorComprehensive:
    """进度监控类 - 综合版本"""

    def __init__(self, total_files, logger):
        self.total_files = total_files
        self.processed_files = 0
        self.failed_files = 0
        self.start_time = time.time()
        self.logger = logger
        self.stage_progress = {}

    def update(self, success=True, stage_info=None):
        if success:
            self.processed_files += 1
        else:
            self.failed_files += 1

        # 更新阶段进度
        if stage_info:
            stage_key = f"{stage_info['stage_type']}_{stage_info['stage_number']}"
            if stage_key not in self.stage_progress:
                self.stage_progress[stage_key] = {'success': 0, 'failed': 0}

            if success:
                self.stage_progress[stage_key]['success'] += 1
            else:
                self.stage_progress[stage_key]['failed'] += 1

        completed = self.processed_files + self.failed_files
        progress = completed / self.total_files * 100

        elapsed_time = time.time() - self.start_time
        if completed > 0:
            avg_time = elapsed_time / completed
            remaining_time = avg_time * (self.total_files - completed)

            self.logger.info(f"进度: {completed}/{self.total_files} ({progress:.1f}%) "
                           f"成功: {self.processed_files} 失败: {self.failed_files} "
                           f"预计剩余: {remaining_time/60:.1f}分钟")

    def stage_summary(self):
        """阶段处理摘要"""
        for stage_key, counts in self.stage_progress.items():
            total = counts['success'] + counts['failed']
            success_rate = counts['success'] / total * 100 if total > 0 else 0
            self.logger.info(f"阶段 {stage_key}: {counts['success']}/{total} 成功 ({success_rate:.1f}%)")

    def final_report(self):
        total_time = time.time() - self.start_time
        success_rate = self.processed_files / self.total_files * 100

        self.logger.info(f"\n=== 处理完成 ===")
        self.logger.info(f"总文件数: {self.total_files}")
        self.logger.info(f"成功处理: {self.processed_files}")
        self.logger.info(f"处理失败: {self.failed_files}")
        self.logger.info(f"成功率: {success_rate:.1f}%")
        self.logger.info(f"总耗时: {total_time/60:.1f}分钟")

        # 阶段摘要
        self.logger.info(f"\n=== 阶段处理摘要 ===")
        self.stage_summary()

def main():
    """主函数 - 综合HEP数据提取"""
    # 设置日志
    logger = setup_logging()

    logger.info("=== HEP数据提取脚本 - 综合重新开发版本 ===")
    logger.info(f"数据源目录: {DATA_DIR}")
    logger.info(f"输出目录: {OUTPUT_DIR}")
    logger.info(f"提取时间窗口: {HEP_TMIN}s 到 {HEP_TMAX}s")
    logger.info(f"显示时间窗口: {DISPLAY_TMIN}s 到 {DISPLAY_TMAX}s")
    logger.info(f"滤波参数: {FILTER_LOW}-{FILTER_HIGH}Hz (无平滑处理)")
    logger.info(f"参考心电: {REFERENCE_ECG}")
    logger.info(f"被试数量: {len(SUBJECTS)}")

    # 计算总文件数
    total_files = 0
    for stage_type, stage_numbers in STAGE_CONFIG.items():
        total_files += len(SUBJECTS) * len(stage_numbers)

    logger.info(f"预计处理文件总数: {total_files}")

    progress_monitor = ProgressMonitorComprehensive(total_files, logger)

    # 按阶段处理数据
    for stage_type, stage_numbers in STAGE_CONFIG.items():
        for stage_number in stage_numbers:
            logger.info(f"\n开始处理阶段: {stage_type}_{stage_number}")

            stage_results = []

            # 处理该阶段的所有被试
            for subject_id in SUBJECTS:
                result = process_single_subject_comprehensive(subject_id, stage_number, stage_type, logger)

                stage_info = {'stage_type': stage_type, 'stage_number': stage_number}

                if result:
                    stage_results.append(result)
                    progress_monitor.update(success=True, stage_info=stage_info)
                else:
                    progress_monitor.update(success=False, stage_info=stage_info)
                    # 根据需求，错误不能跳过，需要进行错误排查和处理
                    logger.error(f"被试 {subject_id:02d} - {stage_type}_{stage_number} 处理失败，需要检查错误原因")

            # 保存该阶段的数据
            if stage_results:
                save_success = save_stage_data_to_hdf5_comprehensive(stage_results, stage_type, stage_number, logger)
                if not save_success:
                    logger.error(f"阶段 {stage_type}_{stage_number} 数据保存失败")
                else:
                    logger.info(f"阶段 {stage_type}_{stage_number} 完成: {len(stage_results)}/{len(SUBJECTS)} 个被试成功")
            else:
                logger.error(f"阶段 {stage_type}_{stage_number} 没有有效数据")

    # 最终报告
    progress_monitor.final_report()
    logger.info("HEP数据提取脚本执行完成!")

if __name__ == "__main__":
    main()
