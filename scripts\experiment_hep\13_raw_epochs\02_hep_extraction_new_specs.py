#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
HEP (Heartbeat Evoked Potential) 数据提取脚本 - 新规格版本

严格按照新需求规格开发：
1. 数据源：.set文件格式，TP9TP10参考
2. 时间窗口：-500ms到+1500ms（以R波为0点）
3. 参考心电：ECG11
4. 输出：HDF5格式，按阶段组织
5. 质量控制：R波单峰检测、基线校正验证、HEP成分清晰度检查

技术参数：
- 时间窗口：-500ms 到 +1500ms
- 滤波参数：0.1-30Hz带通滤波
- 基线校正：-200ms 到 0ms
- 采样率：500Hz
- 总通道：119个（61脑电 + 58心电）
"""

import os
import sys
import numpy as np
import pandas as pd
import mne
import neurokit2 as nk
import h5py
import logging
import time
from scipy import signal
from tqdm import tqdm
import warnings
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')  # 非交互式后端

# 设置中文字体
import matplotlib.font_manager as fm
font_path = r"C:\Users\<USER>\Desktop\BaiduSyncdisk\桌面环境\霞鹜文楷\LXGWWenKai-Medium.ttf"
if os.path.exists(font_path):
    fm.fontManager.addfont(font_path)
    font_prop = fm.FontProperties(fname=font_path)
    plt.rcParams['font.family'] = font_prop.get_name()
    plt.rcParams['font.sans-serif'] = [font_prop.get_name()]
else:
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

# 忽略警告
warnings.filterwarnings('ignore', category=RuntimeWarning)
mne.set_log_level('WARNING')

# ===== 新规格配置参数 =====

# 数据路径配置
DATA_DIR = r"D:\ecgeeg\18-eegecg手动预处理5-重参考2-双侧乳突"
OUTPUT_DIR = r"D:\ecgeeg\30-数据分析\5-HBA\result\hep_analysis\14_rest_vs_test_analysis\hepdata"
PLOTS_DIR = os.path.join(OUTPUT_DIR, 'plots')
LOGS_DIR = os.path.join(OUTPUT_DIR, 'logs')

# 确保输出目录存在
for dir_path in [OUTPUT_DIR, PLOTS_DIR, LOGS_DIR]:
    os.makedirs(dir_path, exist_ok=True)

# 数据结构规格
TOTAL_CHANNELS = 119  # 总通道数
EEG_CHANNELS = 61     # 脑电通道数
ECG_CHANNELS = 58     # 心电通道数
SAMPLING_RATE = 500   # 采样率 Hz

# HEP提取技术参数
HEP_TMIN = -0.5       # R波前500ms
HEP_TMAX = 1.5        # R波后1500ms（新规格）
BASELINE_TMIN = -0.2  # 基线校正开始
BASELINE_TMAX = 0.0   # 基线校正结束
FILTER_LOW = 0.1      # 低频截止
FILTER_HIGH = 30.0    # 高频截止

# 参考心电电极
REFERENCE_ECG = 'ECG11'

# 被试配置（排除03, 04, 14）
SUBJECTS = [1, 2, 5, 6, 7, 8, 9, 10, 11, 12, 13, 15, 16, 17, 18, 19, 20,
           21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32]

# 阶段配置
STAGES = ['prac', 'test', 'rest']
STAGE_NUMBERS = ['01', '02', '03']

# 电极组合定义
CENTRAL_ELECTRODES = ['F1', 'F2', 'FC1', 'FC2', 'C1', 'Cz', 'C2', 'Fz']
RIGHT_HEMISPHERE = ['F2', 'F4', 'F6', 'AF4', 'AF8', 'FP2', 'FC2', 'FC4', 'FC6']
LEFT_HEMISPHERE = ['F1', 'F3', 'F5', 'AF3', 'AF7', 'FP1', 'FC1', 'FC3', 'FC5']

# 质量控制参数
QUALITY_THRESHOLDS = {
    'min_r_peaks': 30,          # 最少R峰数量
    'max_rr_interval': 2.0,     # 最大RR间期（秒）
    'min_rr_interval': 0.4,     # 最小RR间期（秒）
    'baseline_stability': 50.0,  # 基线稳定性阈值（μV）
    'hep_clarity_threshold': 1.5, # HEP成分清晰度阈值
}

def setup_logging():
    """设置日志系统"""
    log_file = os.path.join(LOGS_DIR, f'hep_extraction_new_specs_{time.strftime("%Y%m%d_%H%M%S")}.log')
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    return logging.getLogger(__name__)

def get_file_pattern(subject_id, stage_number, stage_type):
    """根据新规格生成文件名模式"""
    return f"{subject_id:02d}_{stage_number}_reto2_combined_baseline_corrected_chanloc_bad_interp_{stage_type}_TP9TP10Ref.fif"

def load_data_new_format(subject_id, stage_number, stage_type, logger):
    """加载.fif格式的数据文件"""
    try:
        filename = get_file_pattern(subject_id, stage_number, stage_type)
        file_path = os.path.join(DATA_DIR, filename)

        if not os.path.exists(file_path):
            logger.warning(f"文件不存在: {filename}")
            return None

        # 加载.fif文件
        raw = mne.io.read_raw_fif(file_path, preload=True, verbose=False)

        # 验证数据结构
        if len(raw.ch_names) != TOTAL_CHANNELS:
            logger.warning(f"通道数不匹配: 期望{TOTAL_CHANNELS}，实际{len(raw.ch_names)}")
            return None

        if raw.info['sfreq'] != SAMPLING_RATE:
            logger.warning(f"采样率不匹配: 期望{SAMPLING_RATE}Hz，实际{raw.info['sfreq']}Hz")
            return None

        return raw

    except Exception as e:
        logger.error(f"加载数据失败 {filename}: {str(e)}")
        return None

def detect_voltage_unit_and_convert(raw, logger):
    """自动检测电压单位并转换为μV"""
    try:
        # 获取数据样本进行检测
        data_sample = raw.get_data()[:10, :1000]  # 前10个通道的前1000个样本
        
        # 计算典型幅值
        typical_amplitude = np.std(data_sample)
        
        # 判断单位并进行转换
        if typical_amplitude > 1e-3:  # 大于1mV，可能是V单位
            conversion_factor = 1e6
            original_unit = "V"
            logger.info(f"检测到电压单位为V，转换系数: {conversion_factor}")
        elif typical_amplitude > 1e-6:  # 大于1μV，可能是mV单位
            conversion_factor = 1e3
            original_unit = "mV"
            logger.info(f"检测到电压单位为mV，转换系数: {conversion_factor}")
        else:  # 可能已经是μV或更小单位
            conversion_factor = 1.0
            original_unit = "μV"
            logger.info(f"检测到电压单位为μV，无需转换")
        
        # 如果需要转换，应用转换系数
        if conversion_factor != 1.0:
            raw._data = raw._data * conversion_factor
            logger.info(f"数据从{original_unit}转换为μV")
        
        return raw, conversion_factor, original_unit
        
    except Exception as e:
        logger.error(f"电压单位检测失败: {str(e)}")
        return raw, 1.0, "unknown"

def extract_ecg_channel(raw, logger):
    """提取参考心电通道ECG11"""
    try:
        if REFERENCE_ECG not in raw.ch_names:
            logger.error(f"参考心电通道 {REFERENCE_ECG} 不存在")
            return None
        
        # 提取ECG11通道数据
        ecg_idx = raw.ch_names.index(REFERENCE_ECG)
        ecg_signal = raw.get_data()[ecg_idx, :]
        
        logger.info(f"成功提取参考心电通道: {REFERENCE_ECG}")
        return ecg_signal
        
    except Exception as e:
        logger.error(f"提取心电通道失败: {str(e)}")
        return None

def detect_r_peaks_single_peak(ecg_signal, sampling_rate, logger):
    """检测R波峰值，确保单峰检测避免双峰现象"""
    try:
        # 清洗ECG信号
        ecg_cleaned = nk.ecg_clean(ecg_signal, sampling_rate=sampling_rate)
        
        # 使用多种方法检测R峰，选择最佳结果
        methods = ['neurokit', 'pantompkins1985', 'hamilton2002']
        best_peaks = None
        best_score = 0
        
        for method in methods:
            try:
                _, rpeaks_info = nk.ecg_peaks(ecg_cleaned, sampling_rate=sampling_rate, method=method)
                r_peaks = rpeaks_info.get('ECG_R_Peaks', [])
                
                if len(r_peaks) < QUALITY_THRESHOLDS['min_r_peaks']:
                    continue
                
                # 检查双峰问题
                double_peak_ratio = check_double_peak_issue(ecg_cleaned, r_peaks, sampling_rate)
                
                # 计算质量分数（峰数量 + 双峰惩罚）
                quality_score = len(r_peaks) * (1.0 - double_peak_ratio)
                
                if quality_score > best_score:
                    best_score = quality_score
                    best_peaks = r_peaks
                    
                logger.debug(f"方法{method}: {len(r_peaks)}个R峰, 双峰比例: {double_peak_ratio:.3f}, 质量分数: {quality_score:.1f}")
                
            except Exception as e:
                logger.debug(f"方法{method}失败: {str(e)}")
                continue
        
        if best_peaks is None:
            raise ValueError("所有R峰检测方法都失败")
        
        # 精细化R峰位置
        refined_peaks = refine_r_peak_positions(ecg_cleaned, best_peaks, sampling_rate)
        
        logger.info(f"R峰检测完成: 检测到{len(refined_peaks)}个R峰")
        return refined_peaks, ecg_cleaned
        
    except Exception as e:
        logger.error(f"R峰检测失败: {str(e)}")
        return None, None

def check_double_peak_issue(ecg_signal, r_peaks, sampling_rate, window_ms=100):
    """检查R波双峰问题"""
    try:
        window_samples = int(window_ms * sampling_rate / 1000)
        double_peak_count = 0
        
        for peak in r_peaks[:min(20, len(r_peaks))]:  # 检查前20个峰
            start = max(0, peak - window_samples // 2)
            end = min(len(ecg_signal), peak + window_samples // 2)
            
            if start < end:
                local_signal = ecg_signal[start:end]
                # 寻找局部峰值
                from scipy.signal import find_peaks
                peaks_in_window, _ = find_peaks(local_signal, height=np.max(local_signal) * 0.7)
                
                # 如果发现多个峰值，认为是双峰
                if len(peaks_in_window) > 1:
                    double_peak_count += 1
        
        double_peak_ratio = double_peak_count / min(20, len(r_peaks)) if len(r_peaks) > 0 else 0
        return double_peak_ratio
        
    except Exception as e:
        return 0.0

def refine_r_peak_positions(ecg_signal, r_peaks, sampling_rate, window_ms=50):
    """精细化R峰位置到真正的峰值点"""
    try:
        window_samples = int(window_ms * sampling_rate / 1000)
        refined_peaks = []
        
        for peak in r_peaks:
            start = max(0, peak - window_samples // 2)
            end = min(len(ecg_signal), peak + window_samples // 2)
            
            if start < end:
                local_signal = ecg_signal[start:end]
                local_max_idx = np.argmax(local_signal)
                refined_peak = start + local_max_idx
                refined_peaks.append(refined_peak)
            else:
                refined_peaks.append(peak)
        
        return np.array(refined_peaks)
        
    except Exception as e:
        return r_peaks

def apply_filtering(raw, logger):
    """应用0.1-30Hz带通滤波"""
    try:
        raw_filtered = raw.copy()
        raw_filtered.filter(l_freq=FILTER_LOW, h_freq=FILTER_HIGH, 
                           fir_design='firwin', verbose=False)
        
        logger.info(f"应用滤波: {FILTER_LOW}-{FILTER_HIGH}Hz")
        return raw_filtered
        
    except Exception as e:
        logger.error(f"滤波失败: {str(e)}")
        return raw

def extract_hep_epochs(raw, r_peaks, logger):
    """提取HEP epochs"""
    try:
        # 创建事件数组
        events = np.column_stack([r_peaks, np.zeros_like(r_peaks), np.ones_like(r_peaks)])
        
        # 创建epochs
        epochs = mne.Epochs(raw, events, event_id=1,
                           tmin=HEP_TMIN, tmax=HEP_TMAX,
                           baseline=(BASELINE_TMIN, BASELINE_TMAX),
                           preload=True, reject=None, verbose=False)
        
        logger.info(f"创建了 {len(epochs)} 个HEP epochs")
        return epochs
        
    except Exception as e:
        logger.error(f"HEP epochs提取失败: {str(e)}")
        return None

def validate_quality_control(epochs, logger):
    """质量控制验证"""
    try:
        validation_results = {}
        
        # 1. 验证基线校正效果
        baseline_mask = (epochs.times >= BASELINE_TMIN) & (epochs.times <= BASELINE_TMAX)
        baseline_data = epochs.get_data()[:, :EEG_CHANNELS, baseline_mask]
        baseline_std = np.std(baseline_data) * 1e6  # 转换为μV
        
        baseline_stable = baseline_std < QUALITY_THRESHOLDS['baseline_stability']
        validation_results['baseline_stability'] = {
            'std_uv': baseline_std,
            'threshold_uv': QUALITY_THRESHOLDS['baseline_stability'],
            'passed': baseline_stable
        }
        
        # 2. 检查HEP成分清晰度（200ms后）
        hep_mask = (epochs.times >= 0.2) & (epochs.times <= 0.6)
        hep_data = epochs.get_data()[:, :EEG_CHANNELS, hep_mask]
        hep_amplitude = np.std(hep_data) * 1e6  # 转换为μV
        
        hep_clarity_ratio = hep_amplitude / baseline_std if baseline_std > 0 else 0
        hep_clear = hep_clarity_ratio >= QUALITY_THRESHOLDS['hep_clarity_threshold']
        validation_results['hep_clarity'] = {
            'amplitude_uv': hep_amplitude,
            'clarity_ratio': hep_clarity_ratio,
            'threshold': QUALITY_THRESHOLDS['hep_clarity_threshold'],
            'passed': hep_clear
        }
        
        # 3. 整体质量评估
        overall_passed = baseline_stable and hep_clear
        validation_results['overall'] = {
            'passed': overall_passed,
            'epochs_count': len(epochs)
        }
        
        if logger:
            logger.info(f"质量控制 - 基线稳定性: {baseline_std:.2f}μV ({'通过' if baseline_stable else '失败'})")
            logger.info(f"质量控制 - HEP清晰度: {hep_clarity_ratio:.2f} ({'通过' if hep_clear else '失败'})")
            logger.info(f"质量控制 - 整体评估: {'通过' if overall_passed else '失败'}")
        
        return validation_results
        
    except Exception as e:
        if logger:
            logger.error(f"质量控制验证失败: {str(e)}")
        return {'overall': {'passed': False, 'error': str(e)}}

def organize_electrode_data(epochs, logger):
    """按电极组合组织数据"""
    try:
        organized_data = {}
        epochs_data = epochs.get_data()
        ch_names = epochs.ch_names
        
        # 全通道数据（119通道：前61个HEP处理的脑电 + 后58个原始心电）
        organized_data['all_channels_hep'] = epochs_data
        
        # 中央电极数据
        central_indices = []
        for electrode in CENTRAL_ELECTRODES:
            if electrode in ch_names:
                central_indices.append(ch_names.index(electrode))
        
        if central_indices:
            organized_data['central_electrodes_hep'] = epochs_data[:, central_indices, :]
            logger.info(f"中央电极: {len(central_indices)}个电极")
        
        # 右半球电极数据
        right_indices = []
        for electrode in RIGHT_HEMISPHERE:
            if electrode in ch_names:
                right_indices.append(ch_names.index(electrode))
        
        if right_indices:
            organized_data['right_hemisphere_hep'] = epochs_data[:, right_indices, :]
            logger.info(f"右半球电极: {len(right_indices)}个电极")
        
        # 左半球电极数据
        left_indices = []
        for electrode in LEFT_HEMISPHERE:
            if electrode in ch_names:
                left_indices.append(ch_names.index(electrode))
        
        if left_indices:
            organized_data['left_hemisphere_hep'] = epochs_data[:, left_indices, :]
            logger.info(f"左半球电极: {len(left_indices)}个电极")
        
        return organized_data
        
    except Exception as e:
        logger.error(f"数据组织失败: {str(e)}")
        return {}

def create_quality_visualization(epochs, subject_id, stage_type, stage_number, 
                                validation_results, logger):
    """创建质量控制可视化图片"""
    try:
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle(f'Subject {subject_id:02d} - {stage_type}_{stage_number} HEP质量控制', 
                    fontsize=16)
        
        # 时间轴（毫秒）
        times_ms = epochs.times * 1000
        
        # 转换数据为μV
        epochs_data_uv = epochs.get_data() * 1e6
        
        # 1. 中央电极平均波形
        central_indices = []
        for electrode in CENTRAL_ELECTRODES:
            if electrode in epochs.ch_names:
                central_indices.append(epochs.ch_names.index(electrode))
        
        if central_indices:
            central_data = epochs_data_uv[:, central_indices, :]
            central_avg = np.mean(np.mean(central_data, axis=1), axis=0)
            
            axes[0, 0].plot(times_ms, central_avg, 'b-', linewidth=2)
            axes[0, 0].axhline(y=0, color='k', linestyle='--', alpha=0.5)
            axes[0, 0].axvline(x=0, color='r', linestyle='--', alpha=0.7, label='R-peak')
            axes[0, 0].axvspan(BASELINE_TMIN*1000, BASELINE_TMAX*1000, alpha=0.2, color='gray')
            axes[0, 0].set_title('中央电极HEP平均波形')
            axes[0, 0].set_xlabel('时间 (ms)')
            axes[0, 0].set_ylabel('振幅 (μV)')
            axes[0, 0].grid(True, alpha=0.3)
        
        # 2. 基线稳定性检查
        baseline_mask = (times_ms >= BASELINE_TMIN*1000) & (times_ms <= BASELINE_TMAX*1000)
        baseline_data = central_avg[baseline_mask] if central_indices else np.array([])
        
        if len(baseline_data) > 0:
            axes[0, 1].plot(times_ms[baseline_mask], baseline_data, 'g-', linewidth=2)
            axes[0, 1].axhline(y=np.mean(baseline_data), color='r', linestyle='-', alpha=0.7)
            baseline_std = validation_results['baseline_stability']['std_uv']
            axes[0, 1].set_title(f'基线稳定性检查\nStd: {baseline_std:.2f}μV')
            axes[0, 1].set_xlabel('时间 (ms)')
            axes[0, 1].set_ylabel('振幅 (μV)')
            axes[0, 1].grid(True, alpha=0.3)
        
        # 3. HEP成分清晰度（200ms后）
        hep_mask = (times_ms >= 200) & (times_ms <= 600)
        hep_data = central_avg[hep_mask] if central_indices else np.array([])
        
        if len(hep_data) > 0:
            axes[1, 0].plot(times_ms[hep_mask], hep_data, 'm-', linewidth=2)
            hep_clarity = validation_results['hep_clarity']['clarity_ratio']
            axes[1, 0].set_title(f'HEP成分清晰度检查\n清晰度比例: {hep_clarity:.2f}')
            axes[1, 0].set_xlabel('时间 (ms)')
            axes[1, 0].set_ylabel('振幅 (μV)')
            axes[1, 0].grid(True, alpha=0.3)
        
        # 4. 质量控制总结
        axes[1, 1].axis('off')
        summary_text = f"""质量控制总结:

基线稳定性: {'✓ 通过' if validation_results['baseline_stability']['passed'] else '✗ 失败'}
  标准差: {validation_results['baseline_stability']['std_uv']:.2f}μV
  阈值: {validation_results['baseline_stability']['threshold_uv']:.1f}μV

HEP清晰度: {'✓ 通过' if validation_results['hep_clarity']['passed'] else '✗ 失败'}
  清晰度比例: {validation_results['hep_clarity']['clarity_ratio']:.2f}
  阈值: {validation_results['hep_clarity']['threshold']:.1f}

总体评估: {'✓ 通过' if validation_results['overall']['passed'] else '✗ 失败'}
Epochs数量: {validation_results['overall']['epochs_count']}个"""
        
        axes[1, 1].text(0.1, 0.9, summary_text, transform=axes[1, 1].transAxes,
                        fontsize=12, verticalalignment='top',
                        bbox=dict(boxstyle="round,pad=0.3", facecolor='lightblue', alpha=0.8))
        
        plt.tight_layout()
        
        # 保存图片
        filename = f"{subject_id:02d}_{stage_type}_{stage_number}_quality_control.png"
        filepath = os.path.join(PLOTS_DIR, filename)
        plt.savefig(filepath, dpi=150, bbox_inches='tight')
        plt.close()
        
        logger.info(f"保存质量控制图片: {filename}")
        return filepath
        
    except Exception as e:
        logger.error(f"创建质量可视化失败: {str(e)}")
        return None

def process_single_subject(subject_id, stage_number, stage_type, logger):
    """处理单个被试的数据"""
    try:
        logger.info(f"开始处理被试 {subject_id:02d} - {stage_type}_{stage_number}")
        
        # 1. 加载数据
        raw = load_data_new_format(subject_id, stage_number, stage_type, logger)
        if raw is None:
            return None
        
        # 2. 检测并转换电压单位
        raw, conversion_factor, original_unit = detect_voltage_unit_and_convert(raw, logger)
        
        # 3. 应用滤波
        raw_filtered = apply_filtering(raw, logger)
        
        # 4. 提取参考心电通道
        ecg_signal = extract_ecg_channel(raw_filtered, logger)
        if ecg_signal is None:
            return None
        
        # 5. R波检测（单峰检测）
        r_peaks, ecg_cleaned = detect_r_peaks_single_peak(ecg_signal, SAMPLING_RATE, logger)
        if r_peaks is None:
            return None
        
        # 6. 提取HEP epochs
        epochs = extract_hep_epochs(raw_filtered, r_peaks, logger)
        if epochs is None:
            return None
        
        # 7. 质量控制验证
        validation_results = validate_quality_control(epochs, logger)
        
        # 8. 组织电极数据
        organized_data = organize_electrode_data(epochs, logger)
        
        # 9. 创建质量可视化
        create_quality_visualization(epochs, subject_id, stage_type, stage_number, 
                                   validation_results, logger)
        
        logger.info(f"成功处理被试 {subject_id:02d} - {stage_type}_{stage_number}")
        
        return {
            'subject_id': subject_id,
            'organized_data': organized_data,
            'validation_results': validation_results,
            'epochs_count': len(epochs)
        }
        
    except Exception as e:
        logger.error(f"处理被试 {subject_id:02d} 失败: {str(e)}")
        return None

def save_stage_data_to_hdf5(stage_data_list, stage_type, stage_number, logger):
    """将阶段数据保存为HDF5文件"""
    try:
        # 创建文件名
        filename = f"{stage_type}_{stage_number}_hep_data.h5"
        filepath = os.path.join(OUTPUT_DIR, filename)
        
        logger.info(f"开始保存阶段数据: {filename}")
        
        # 合并所有被试的数据
        combined_data = {}
        valid_subjects = []
        
        for result in stage_data_list:
            if result and result['organized_data']:
                valid_subjects.append(result['subject_id'])
                
                for data_type, data in result['organized_data'].items():
                    if data_type not in combined_data:
                        combined_data[data_type] = []
                    combined_data[data_type].append(data)
        
        # 保存到HDF5文件
        with h5py.File(filepath, 'w') as f:
            # 保存元数据
            f.attrs['stage_type'] = stage_type
            f.attrs['stage_number'] = stage_number
            f.attrs['n_subjects'] = len(valid_subjects)
            f.attrs['subject_ids'] = valid_subjects
            f.attrs['creation_time'] = time.strftime('%Y-%m-%d %H:%M:%S')
            
            # 保存各类电极数据
            for data_type, data_list in combined_data.items():
                if data_list:
                    # 合并所有被试的数据
                    combined_array = np.concatenate(data_list, axis=0)
                    dataset = f.create_dataset(data_type, data=combined_array, compression='gzip')
                    
                    # 添加数据集属性
                    dataset.attrs['description'] = f'{data_type} from {len(data_list)} subjects'
                    dataset.attrs['shape_info'] = f'(epochs, channels, timepoints) = {combined_array.shape}'
                    
                    logger.info(f"保存 {data_type}: {combined_array.shape}")
        
        logger.info(f"成功保存HDF5文件: {filename} ({len(valid_subjects)}个被试)")
        return True
        
    except Exception as e:
        logger.error(f"保存HDF5文件失败: {str(e)}")
        return False

class ProgressMonitor:
    """进度监控类"""
    
    def __init__(self, total_files, logger):
        self.total_files = total_files
        self.processed_files = 0
        self.failed_files = 0
        self.start_time = time.time()
        self.logger = logger
    
    def update(self, success=True):
        if success:
            self.processed_files += 1
        else:
            self.failed_files += 1
        
        completed = self.processed_files + self.failed_files
        progress = completed / self.total_files * 100
        
        elapsed_time = time.time() - self.start_time
        if completed > 0:
            avg_time = elapsed_time / completed
            remaining_time = avg_time * (self.total_files - completed)
            
            self.logger.info(f"进度: {completed}/{self.total_files} ({progress:.1f}%) "
                           f"成功: {self.processed_files} 失败: {self.failed_files} "
                           f"预计剩余: {remaining_time/60:.1f}分钟")
    
    def final_report(self):
        total_time = time.time() - self.start_time
        success_rate = self.processed_files / self.total_files * 100
        
        self.logger.info(f"\n=== 处理完成 ===")
        self.logger.info(f"总文件数: {self.total_files}")
        self.logger.info(f"成功处理: {self.processed_files}")
        self.logger.info(f"处理失败: {self.failed_files}")
        self.logger.info(f"成功率: {success_rate:.1f}%")
        self.logger.info(f"总耗时: {total_time/60:.1f}分钟")

def main():
    """主函数"""
    # 设置日志
    logger = setup_logging()
    
    logger.info("=== HEP数据提取脚本 - 新规格版本 ===")
    logger.info(f"数据源目录: {DATA_DIR}")
    logger.info(f"输出目录: {OUTPUT_DIR}")
    logger.info(f"时间窗口: {HEP_TMIN}s 到 {HEP_TMAX}s")
    logger.info(f"滤波参数: {FILTER_LOW}-{FILTER_HIGH}Hz")
    logger.info(f"参考心电: {REFERENCE_ECG}")
    logger.info(f"被试数量: {len(SUBJECTS)}")
    logger.info(f"阶段配置: {len(STAGES)} × {len(STAGE_NUMBERS)} = {len(STAGES) * len(STAGE_NUMBERS)}")
    
    # 计算总文件数
    total_files = len(SUBJECTS) * len(STAGE_NUMBERS) * len(STAGES)
    progress_monitor = ProgressMonitor(total_files, logger)
    
    # 按阶段处理数据
    for stage_type in STAGES:
        for stage_number in STAGE_NUMBERS:
            logger.info(f"\n开始处理阶段: {stage_type}_{stage_number}")
            
            stage_results = []
            
            # 处理该阶段的所有被试
            for subject_id in SUBJECTS:
                result = process_single_subject(subject_id, stage_number, stage_type, logger)
                
                if result:
                    stage_results.append(result)
                    progress_monitor.update(success=True)
                else:
                    progress_monitor.update(success=False)
                    # 错误不能跳过，需要报告
                    logger.error(f"被试 {subject_id:02d} 处理失败，需要检查错误原因")
            
            # 保存该阶段的数据
            if stage_results:
                save_success = save_stage_data_to_hdf5(stage_results, stage_type, stage_number, logger)
                if not save_success:
                    logger.error(f"阶段 {stage_type}_{stage_number} 数据保存失败")
                else:
                    logger.info(f"阶段 {stage_type}_{stage_number} 完成: {len(stage_results)}/{len(SUBJECTS)} 个被试成功")
            else:
                logger.error(f"阶段 {stage_type}_{stage_number} 没有有效数据")
    
    # 最终报告
    progress_monitor.final_report()
    logger.info("HEP数据提取脚本执行完成!")

if __name__ == "__main__":
    main() 