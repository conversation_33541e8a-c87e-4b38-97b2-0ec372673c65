{"subject_id": 25, "stage_type": "prac", "stage_number": "01", "processing_time": 6.408314228057861, "special_processing": {"is_problematic_subject": false, "special_config": null, "description": "N/A"}, "ecg_channel_selection": {"selected_channel": "ECG8", "quality_scores": {"ECG11": {"quality_score": 0.40000217618118994, "r_peaks_count": 100, "rr_cv": 0.6159675492442908, "signal_std": 0.010880905949625914, "cleaned_signal": "[-0.04995098 -0.0458749  -0.0418024  ... -0.02109263 -0.02298293\n -0.02501726]"}, "ECG7": {"quality_score": 0.40000094188209895, "r_peaks_count": 106, "rr_cv": 0.4189807457272926, "signal_std": 0.004709410494733129, "cleaned_signal": "[0.03113867 0.02778151 0.02443732 ... 0.0290822  0.02718764 0.02528729]"}, "ECG8": {"quality_score": 0.4908194709258195, "r_peaks_count": 129, "rr_cv": 0.1545902773348913, "signal_std": 0.00012797801037782043, "cleaned_signal": "[-3.34688693e-06 -4.81105064e-05 -9.13283003e-05 ... -9.68823215e-04\n -8.67578531e-04 -7.64423696e-04]"}, "ECG12": {"quality_score": 0.4000017100395945, "r_peaks_count": 134, "rr_cv": 0.49374584127986504, "signal_std": 0.008550197972298073, "cleaned_signal": "[-0.05213829 -0.04653662 -0.04098051 ...  0.02229102  0.02275783\n  0.02330981]"}}}, "r_peak_detection": {"method": "neurokit", "original_count": 129, "final_count": 129, "quality_score": 109.05785422379903}, "r_peak_alignment": {"expected_position_ms": 0.0, "actual_position_ms": 0.0, "position_error_ms": 0.0, "tolerance_ms": 10.0, "alignment_passed": "True"}, "epochs_quality": {"baseline_std": 6.325449997428754, "hep_std": 9.36062562900059, "max_amplitude": 477.25687068741064, "snr": 1.4798355267697334, "hep_change_ratio": 1.4798355267697334, "r_wave_amplitude": 320.3246663115061, "final_noise_level": 6.325449997428754, "target_noise_level": 25.0, "noise_control_success": "True", "processing_note": "标准处理", "artifact_threshold": 10000.0, "baseline_threshold": 40.0, "issues": ["R波伪迹过强: 320.3μV"], "passed": true, "validation_status": "passed_strict_validation"}, "final_epochs_count": 129, "data_shapes": {"all_channels_hep": [129, 119, 751], "central_electrodes_hep": [129, 8, 751], "right_hemisphere_hep": [129, 8, 751], "left_hemisphere_hep": [129, 8, 751]}, "processing_success": true, "quality_control_version": "strict_physiological_standards_v1.0"}