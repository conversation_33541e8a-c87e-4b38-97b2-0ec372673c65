#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
HEP (Heartbeat Evoked Potential) 数据提取脚本 - 数据质量优先版本

核心原则：数据质量问题应通过改善数据预处理来解决，而不是放宽验证标准

实施策略：
1. 恢复严格验证标准
2. 多层次数据预处理策略
3. 验证失败时进行数据质量改善而非放宽标准
4. 特别关注问题被试的深度数据修复
"""

import os
import sys
import numpy as np
import mne
import neurokit2 as nk
import h5py
import logging
import time
import argparse
from scipy import signal
import warnings
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import json

# 设置日志
def setup_logging(background_mode=False):
    """设置日志系统"""
    log_format = '%(asctime)s - %(levelname)s - %(message)s'
    if background_mode:
        logging.basicConfig(
            level=logging.INFO,
            format=log_format,
            handlers=[
                logging.FileHandler('hep_extraction_quality_first.log'),
                logging.StreamHandler(sys.stdout)
            ]
        )
    else:
        logging.basicConfig(level=logging.INFO, format=log_format)

    return logging.getLogger(__name__)

# 忽略MNE的一些警告
warnings.filterwarnings('ignore', category=RuntimeWarning)
mne.set_log_level('WARNING')

# 数据路径配置
DATA_DIR = r"D:\ecgeeg\18-eegecg手动预处理5-重参考2-双侧乳突"
OUTPUT_DIR = r"D:\ecgeeg\30-数据分析\5-HBA\result\hep_analysis\14_rest_vs_test_analysis\hepdata"
PLOTS_DIR = os.path.join(OUTPUT_DIR, 'plots')
REPORTS_DIR = os.path.join(OUTPUT_DIR, 'quality_reports')
VALIDATION_DIR = os.path.join(OUTPUT_DIR, 'validation_reports')
QUALITY_ANALYSIS_DIR = os.path.join(OUTPUT_DIR, 'quality_analysis')
os.makedirs(OUTPUT_DIR, exist_ok=True)
os.makedirs(PLOTS_DIR, exist_ok=True)
os.makedirs(REPORTS_DIR, exist_ok=True)
os.makedirs(VALIDATION_DIR, exist_ok=True)
os.makedirs(QUALITY_ANALYSIS_DIR, exist_ok=True)

# 实验参数配置
SAMPLING_RATE = 500  # 采样率 Hz
TOTAL_CHANNELS = 119  # 总通道数（61脑电 + 58心电）
EEG_CHANNEL_COUNT = 61  # 脑电通道数
ECG_CHANNEL_COUNT = 58  # 心电通道数

# HEP提取技术参数
HEP_TMIN = -0.5  # R波前500ms（数据提取窗口）
HEP_TMAX = 1.0   # R波后1000ms（数据提取窗口）
VIS_TMIN = -0.2  # 可视化窗口开始：R波前200ms
VIS_TMAX = 0.65  # 可视化窗口结束：R波后650ms
BASELINE_TMIN = -0.2  # 基线校正开始时间
BASELINE_TMAX = 0.0   # 基线校正结束时间

# 多层次滤波参数
FILTER_PARAMS = {
    'standard': {'low': 0.1, 'high': 30.0},      # 标准滤波
    'enhanced': {'low': 0.5, 'high': 25.0},      # 增强去伪迹滤波
    'strict': {'low': 1.0, 'high': 20.0}         # 严格滤波
}

# ECG导联候选列表（按优先级排序）
ECG_CANDIDATES = ['ECG11', 'ECG7', 'ECG8', 'ECG12']

# 被试配置（排除03, 04, 14）
SUBJECTS = [1, 2, 5, 6, 7, 8, 9, 10, 11, 12, 13, 15, 16, 17, 18, 19, 20,
           21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32]

# 阶段配置
STAGES = ['prac', 'test', 'rest']
STAGE_NUMBERS = ['01', '02', '03']

# 电极组合定义
CENTRAL_ELECTRODES = ['F1', 'F2', 'FC1', 'FC2', 'C1', 'Cz', 'C2', 'Fz']
RIGHT_HEMISPHERE = ['F2', 'F4', 'F6', 'AF4', 'AF8', 'FP2', 'FC2', 'FC4', 'FC6']
LEFT_HEMISPHERE = ['F1', 'F3', 'F5', 'AF3', 'AF7', 'FP1', 'FC1', 'FC3', 'FC5']

# 严格验证标准 - 恢复生理学合理范围
STRICT_VALIDATION_STANDARDS = {
    'time_window': {
        'min_time': VIS_TMIN * 1000,  # -200ms
        'max_time': VIS_TMAX * 1000,  # +650ms
        'tolerance': 5.0  # ±5ms容差
    },
    'subplot_aspect_ratio': {
        'target_ratio': 2.0,  # 2:1比例
        'tolerance': 0.1  # ±0.1容差
    },
    'r_peak_alignment': {
        'target_position': 0.0,  # 0ms位置
        'tolerance': 10.0  # ±10ms容差
    },
    'hep_quality': {
        'baseline_std_max': 30.0,  # 基线标准差最大值（μV）- 严格标准
        'hep_change_min_ratio': 2.0,  # HEP变化最小倍数 - 严格标准
        'hep_window_start': 200.0  # HEP成分检查开始时间（ms）
    },
    'r_peak_visibility': {
        'peak_window': 50.0,  # R峰检查窗口（±50ms）
        'min_prominence': 0.1,  # 最小突出度
        'max_position_error': 5  # 最大位置误差（采样点数）- 严格标准±10ms
    }
}

# R波检测算法列表（按优先级排序）
R_PEAK_METHODS = ['neurokit', 'pantompkins1985', 'hamilton2002', 'engzeemod2012', 'elgendi2010']

# 质量控制参数 - 保持合理标准
QUALITY_CONTROL = {
    'min_r_peaks': 30,           # 最少R峰数量
    'max_rr_interval': 1.5,      # 最大RR间期（秒）
    'min_rr_interval': 0.4,      # 最小RR间期（秒）
    'baseline_std_threshold': 100.0,  # 基线标准差阈值（μV）
    'artifact_threshold': 2000.0,    # 伪迹阈值（μV）
}

def get_file_pattern(subject_id, stage_number, stage_type):
    """根据被试ID、阶段编号和阶段类型生成文件名模式"""
    return f"{subject_id:02d}_{stage_number}_reto2_combined_baseline_corrected_chanloc_bad_interp_{stage_type}_TP9TP10Ref.fif"

def load_data(subject_id, stage_number, stage_type, logger):
    """加载指定被试和阶段的数据"""
    try:
        filename = get_file_pattern(subject_id, stage_number, stage_type)
        file_path = os.path.join(DATA_DIR, filename)

        if not os.path.exists(file_path):
            logger.warning(f"文件不存在: {filename}")
            return None

        raw = mne.io.read_raw_fif(file_path, preload=True, verbose=False)

        # 验证数据结构
        if len(raw.ch_names) != TOTAL_CHANNELS:
            logger.warning(f"通道数不匹配: 期望{TOTAL_CHANNELS}，实际{len(raw.ch_names)}")
            return None

        if raw.info['sfreq'] != SAMPLING_RATE:
            logger.warning(f"采样率不匹配: 期望{SAMPLING_RATE}Hz，实际{raw.info['sfreq']}Hz")
            return None

        return raw

    except Exception as e:
        logger.error(f"加载数据失败 {filename}: {str(e)}")
        return None

class DataQualityAnalyzer:
    """数据质量分析器"""

    def __init__(self, logger):
        self.logger = logger

    def analyze_signal_quality(self, raw, subject_id):
        """分析信号质量"""
        quality_report = {
            'subject_id': subject_id,
            'analysis_time': time.strftime('%Y-%m-%d %H:%M:%S'),
            'signal_quality': {},
            'artifact_analysis': {},
            'recommendations': []
        }

        try:
            # 分析EEG信号质量
            eeg_data = raw.get_data()[:EEG_CHANNEL_COUNT, :]
            eeg_std = np.std(eeg_data, axis=1)
            eeg_mean_std = np.mean(eeg_std)

            quality_report['signal_quality']['eeg_mean_std'] = eeg_mean_std
            quality_report['signal_quality']['eeg_max_std'] = np.max(eeg_std)
            quality_report['signal_quality']['eeg_min_std'] = np.min(eeg_std)

            # 检测高幅度伪迹
            artifact_threshold = 200.0  # μV
            eeg_data_uv = eeg_data * 1e6
            artifact_samples = np.sum(np.abs(eeg_data_uv) > artifact_threshold)
            artifact_percentage = artifact_samples / eeg_data_uv.size * 100

            quality_report['artifact_analysis']['high_amplitude_artifacts'] = {
                'threshold_uv': artifact_threshold,
                'artifact_samples': int(artifact_samples),
                'artifact_percentage': artifact_percentage
            }

            # 分析ECG信号质量
            ecg_quality = {}
            for ecg_ch in ECG_CANDIDATES:
                if ecg_ch in raw.ch_names:
                    ecg_data = raw.get_data(picks=[ecg_ch])[0]
                    ecg_std = np.std(ecg_data)
                    ecg_quality[ecg_ch] = {
                        'std': ecg_std,
                        'max_amplitude': np.max(np.abs(ecg_data)),
                        'signal_range': np.max(ecg_data) - np.min(ecg_data)
                    }

            quality_report['signal_quality']['ecg_channels'] = ecg_quality

            # 生成建议
            if artifact_percentage > 5.0:
                quality_report['recommendations'].append('需要增强伪迹去除处理')

            if eeg_mean_std > 50e-6:  # 50μV
                quality_report['recommendations'].append('需要更严格的滤波参数')

            # 检查ECG信号质量
            poor_ecg_channels = []
            for ch, quality in ecg_quality.items():
                if quality['std'] < 1e-5 or quality['signal_range'] < 1e-4:
                    poor_ecg_channels.append(ch)

            if poor_ecg_channels:
                quality_report['recommendations'].append(f'ECG导联质量差: {poor_ecg_channels}')

            return quality_report

        except Exception as e:
            self.logger.error(f"信号质量分析失败: {str(e)}")
            quality_report['error'] = str(e)
            return quality_report

    def save_quality_analysis(self, quality_report):
        """保存质量分析报告"""
        try:
            subject_id = quality_report['subject_id']
            filename = f"subject_{subject_id:02d}_quality_analysis.json"
            filepath = os.path.join(QUALITY_ANALYSIS_DIR, filename)

            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(quality_report, f, indent=2, ensure_ascii=False, default=str)

            self.logger.info(f"保存质量分析报告: {filename}")

        except Exception as e:
            self.logger.error(f"保存质量分析报告失败: {str(e)}")

class MultiLevelPreprocessor:
    """多层次数据预处理器"""

    def __init__(self, logger):
        self.logger = logger

    def apply_standard_filtering(self, raw):
        """应用标准滤波"""
        try:
            raw_filtered = raw.copy()
            params = FILTER_PARAMS['standard']
            raw_filtered.filter(l_freq=params['low'], h_freq=params['high'],
                               fir_design='firwin', verbose=False)

            self.logger.info(f"应用标准滤波: {params['low']}-{params['high']}Hz")
            return raw_filtered

        except Exception as e:
            self.logger.error(f"标准滤波失败: {str(e)}")
            return raw

    def apply_enhanced_preprocessing(self, raw):
        """应用增强预处理（针对验证失败的被试）"""
        try:
            raw_enhanced = raw.copy()

            # 1. 更严格的滤波
            params = FILTER_PARAMS['enhanced']
            raw_enhanced.filter(l_freq=params['low'], h_freq=params['high'],
                               fir_design='firwin', verbose=False)

            # 2. 增强的伪迹去除
            # 使用ICA去除眼电和肌电伪迹
            from mne.preprocessing import ICA

            # 对EEG数据应用ICA
            eeg_picks = mne.pick_types(raw_enhanced.info, eeg=True, ecg=False)
            if len(eeg_picks) > 10:  # 确保有足够的通道进行ICA
                ica = ICA(n_components=min(20, len(eeg_picks)), random_state=42)
                ica.fit(raw_enhanced, picks=eeg_picks)

                # 自动检测眼电成分
                eog_indices, eog_scores = ica.find_bads_eog(raw_enhanced, threshold=2.0)
                if eog_indices:
                    ica.exclude = eog_indices
                    raw_enhanced = ica.apply(raw_enhanced)
                    self.logger.info(f"ICA去除眼电成分: {len(eog_indices)}个")

            # 3. 增强的基线校正
            raw_enhanced.apply_baseline((None, 0))

            self.logger.info(f"应用增强预处理: {params['low']}-{params['high']}Hz + ICA")
            return raw_enhanced

        except Exception as e:
            self.logger.error(f"增强预处理失败: {str(e)}")
            # 如果增强预处理失败，返回标准滤波结果
            return self.apply_standard_filtering(raw)

    def apply_strict_preprocessing(self, raw):
        """应用严格预处理（针对重复失败的被试）"""
        try:
            raw_strict = raw.copy()

            # 1. 最严格的滤波
            params = FILTER_PARAMS['strict']
            raw_strict.filter(l_freq=params['low'], h_freq=params['high'],
                             fir_design='firwin', verbose=False)

            # 2. 手动伪迹检测和去除
            # 检测高幅度伪迹
            data = raw_strict.get_data()
            artifact_threshold = 150e-6  # 150μV

            # 标记包含伪迹的时间段
            artifact_mask = np.any(np.abs(data[:EEG_CHANNEL_COUNT, :]) > artifact_threshold, axis=0)

            if np.sum(artifact_mask) > 0:
                # 插值替换伪迹段
                clean_indices = ~artifact_mask
                if np.sum(clean_indices) > len(artifact_mask) * 0.5:  # 至少50%数据是干净的
                    for ch_idx in range(EEG_CHANNEL_COUNT):
                        if np.any(artifact_mask):
                            # 使用线性插值替换伪迹
                            data[ch_idx, artifact_mask] = np.interp(
                                np.where(artifact_mask)[0],
                                np.where(clean_indices)[0],
                                data[ch_idx, clean_indices]
                            )

                    # 更新数据
                    raw_strict._data = data
                    self.logger.info(f"插值修复伪迹: {np.sum(artifact_mask)}个采样点")

            # 3. 多次基线校正
            raw_strict.apply_baseline((None, 0))

            self.logger.info(f"应用严格预处理: {params['low']}-{params['high']}Hz + 伪迹修复")
            return raw_strict

        except Exception as e:
            self.logger.error(f"严格预处理失败: {str(e)}")
            # 如果严格预处理失败，返回增强预处理结果
            return self.apply_enhanced_preprocessing(raw)

def evaluate_ecg_quality(ecg_signal, sampling_rate):
    """评估ECG信号质量"""
    try:
        # 清洗信号
        ecg_cleaned = nk.ecg_clean(ecg_signal, sampling_rate=sampling_rate)

        # 检测R峰
        _, rpeaks_info = nk.ecg_peaks(ecg_cleaned, sampling_rate=sampling_rate)
        r_peaks = rpeaks_info.get('ECG_R_Peaks', [])

        if len(r_peaks) < 5:
            return {'quality_score': 0.0, 'r_peaks_count': 0, 'rr_cv': float('inf')}

        # 计算RR间期变异系数
        rr_intervals = np.diff(r_peaks) / sampling_rate
        rr_mean = np.mean(rr_intervals)
        rr_std = np.std(rr_intervals)
        rr_cv = rr_std / rr_mean if rr_mean > 0 else float('inf')

        # 计算信号质量分数
        peak_score = min(len(r_peaks) / 100, 1.0)  # 归一化到0-1
        cv_score = max(0, 1.0 - rr_cv * 5)  # CV越小越好
        amplitude_score = min(np.std(ecg_cleaned) / 1000, 1.0)  # 适当的信号幅度

        quality_score = (peak_score * 0.4 + cv_score * 0.4 + amplitude_score * 0.2)

        return {
            'quality_score': quality_score,
            'r_peaks_count': len(r_peaks),
            'rr_cv': rr_cv,
            'signal_std': np.std(ecg_cleaned),
            'cleaned_signal': ecg_cleaned
        }

    except Exception as e:
        return {'quality_score': 0.0, 'r_peaks_count': 0, 'rr_cv': float('inf'), 'error': str(e)}

def select_best_ecg_channel(raw, logger=None):
    """自动选择最佳ECG导联"""
    best_channel = None
    best_quality = -1
    best_signal = None
    quality_report = {}

    # 检查可用的ECG通道
    available_ecg = [ch for ch in ECG_CANDIDATES if ch in raw.ch_names]

    if not available_ecg:
        # 如果指定的通道都不存在，查找所有ECG通道
        available_ecg = [ch for ch in raw.ch_names if ch.startswith('ECG')]

    if not available_ecg:
        raise ValueError("未找到任何ECG通道")

    if logger:
        logger.info(f"评估ECG通道: {available_ecg}")

    # 评估每个ECG通道的质量
    for ch_name in available_ecg:
        try:
            ecg_data = raw.get_data(picks=[ch_name])[0]
            quality_info = evaluate_ecg_quality(ecg_data, raw.info['sfreq'])

            quality_report[ch_name] = quality_info

            if quality_info['quality_score'] > best_quality:
                best_quality = quality_info['quality_score']
                best_channel = ch_name
                best_signal = quality_info.get('cleaned_signal', ecg_data)

            if logger:
                logger.info(f"{ch_name}: 质量分数={quality_info['quality_score']:.3f}, "
                          f"R峰数={quality_info['r_peaks_count']}, "
                          f"RR变异系数={quality_info['rr_cv']:.3f}")

        except Exception as e:
            if logger:
                logger.warning(f"评估{ch_name}失败: {str(e)}")
            quality_report[ch_name] = {'quality_score': 0.0, 'error': str(e)}

    if best_channel is None:
        raise ValueError("无法找到合适的ECG通道")

    if logger:
        logger.info(f"选择最佳ECG通道: {best_channel} (质量分数: {best_quality:.3f})")

    return best_channel, best_signal, quality_report

def detect_r_peaks_optimized(ecg_signal, sampling_rate, logger=None):
    """优化的R波检测算法，尝试多种方法确保最佳检测效果"""
    best_r_peaks = np.array([])
    best_method = None
    best_quality = 0

    for method in R_PEAK_METHODS:
        try:
            # 使用不同的R峰检测方法
            _, rpeaks_info = nk.ecg_peaks(ecg_signal, sampling_rate=sampling_rate, method=method)
            r_peaks = rpeaks_info.get('ECG_R_Peaks', [])

            if len(r_peaks) < QUALITY_CONTROL['min_r_peaks']:
                continue

            # 评估检测质量
            rr_intervals = np.diff(r_peaks) / sampling_rate

            # 过滤异常的RR间期
            valid_mask = (rr_intervals >= QUALITY_CONTROL['min_rr_interval']) & \
                        (rr_intervals <= QUALITY_CONTROL['max_rr_interval'])

            if np.sum(valid_mask) < len(rr_intervals) * 0.7:  # 至少70%的RR间期有效
                continue

            # 计算质量分数
            rr_cv = np.std(rr_intervals) / np.mean(rr_intervals)
            quality_score = len(r_peaks) * (1.0 - min(rr_cv, 1.0))

            if quality_score > best_quality:
                best_quality = quality_score
                best_r_peaks = r_peaks
                best_method = method

            if logger:
                logger.debug(f"方法 {method}: {len(r_peaks)} 个R峰, 质量分数: {quality_score:.2f}")

        except Exception as e:
            if logger:
                logger.debug(f"方法 {method} 失败: {str(e)}")
            continue

    if len(best_r_peaks) == 0:
        raise ValueError("所有R峰检测方法都失败")

    # 进一步优化R峰位置（精确对齐）
    optimized_r_peaks = refine_r_peak_positions(ecg_signal, best_r_peaks, sampling_rate)

    quality_info = {
        'method': best_method,
        'original_count': len(best_r_peaks),
        'final_count': len(optimized_r_peaks),
        'quality_score': best_quality
    }

    if logger:
        logger.info(f"R峰检测完成: 方法={best_method}, 检测到{len(optimized_r_peaks)}个R峰")

    return optimized_r_peaks, best_method, quality_info

def refine_r_peak_positions(ecg_signal, r_peaks, sampling_rate, window_ms=50):
    """精确调整R峰位置，确保对齐到真正的峰值点"""
    window_samples = int(window_ms * sampling_rate / 1000)
    refined_peaks = []

    for peak in r_peaks:
        # 在峰值周围搜索真正的最大值
        start = max(0, peak - window_samples // 2)
        end = min(len(ecg_signal), peak + window_samples // 2)

        if start < end:
            local_signal = ecg_signal[start:end]
            local_max_idx = np.argmax(local_signal)
            refined_peak = start + local_max_idx
            refined_peaks.append(refined_peak)
        else:
            refined_peaks.append(peak)

    return np.array(refined_peaks)

def extract_hep_epochs(raw, r_peaks, logger=None):
    """提取心跳诱发电位epochs"""
    try:
        # 创建事件数组
        events = np.column_stack([r_peaks, np.zeros_like(r_peaks), np.ones_like(r_peaks)])

        # 创建epochs（使用数据提取窗口）
        epochs = mne.Epochs(raw, events, event_id=1,
                           tmin=HEP_TMIN, tmax=HEP_TMAX,
                           baseline=(BASELINE_TMIN, BASELINE_TMAX),
                           preload=True, reject=None, verbose=False)

        if logger:
            logger.info(f"创建了 {len(epochs)} 个HEP epochs")
        return epochs

    except Exception as e:
        if logger:
            logger.error(f"HEP epochs提取失败: {str(e)}")
        return None

def organize_electrode_data(epochs_data, ch_names, logger=None):
    """按照电极组合组织数据"""
    try:
        organized_data = {}

        # 全通道数据（前61个脑电 + 后58个心电）
        organized_data['all_channels_hep'] = epochs_data

        # 中央电极数据
        central_indices = []
        for electrode in CENTRAL_ELECTRODES:
            if electrode in ch_names:
                central_indices.append(ch_names.index(electrode))

        if central_indices:
            organized_data['central_electrodes_hep'] = epochs_data[:, central_indices, :]

        # 右半球电极数据
        right_indices = []
        for electrode in RIGHT_HEMISPHERE:
            if electrode in ch_names:
                right_indices.append(ch_names.index(electrode))

        if right_indices:
            organized_data['right_hemisphere_hep'] = epochs_data[:, right_indices, :]

        # 左半球电极数据
        left_indices = []
        for electrode in LEFT_HEMISPHERE:
            if electrode in ch_names:
                left_indices.append(ch_names.index(electrode))

        if left_indices:
            organized_data['left_hemisphere_hep'] = epochs_data[:, left_indices, :]

        return organized_data

    except Exception as e:
        if logger:
            logger.error(f"数据组织失败: {str(e)}")
        return {}

class StrictQualityValidator:
    """严格质量验证器 - 不放宽标准"""

    def __init__(self, logger):
        self.logger = logger
        self.validation_standards = STRICT_VALIDATION_STANDARDS

    def validate_all_criteria(self, subject_id, stage_type, stage_number,
                             image_path, epochs, ecg_signal, r_peaks, sampling_rate):
        """运行所有严格验证标准"""
        validation_results = {}

        self.logger.info(f"开始严格验证被试 {subject_id:02d} - {stage_type}_{stage_number}")

        # 验证1: 时间窗口
        validation_results['time_window'] = self.validate_time_window()

        # 验证2: 子图比例
        validation_results['subplot_ratio'] = self.validate_subplot_aspect_ratio()

        # 验证3: R波对齐
        validation_results['r_peak_alignment'] = self.validate_r_peak_alignment(epochs)

        # 验证4: HEP质量
        validation_results['hep_quality'] = self.validate_hep_quality(epochs)

        # 验证5: R波可见性
        validation_results['r_peak_visibility'] = self.validate_r_peak_visibility(
            epochs, ecg_signal, r_peaks, sampling_rate)

        # 汇总验证结果
        all_passed = all(result['passed'] for result in validation_results.values())
        failed_tests = [test_name for test_name, result in validation_results.items()
                       if not result['passed']]

        summary = {
            'subject_id': subject_id,
            'stage_type': stage_type,
            'stage_number': stage_number,
            'all_passed': all_passed,
            'passed_count': sum(1 for result in validation_results.values() if result['passed']),
            'total_count': len(validation_results),
            'failed_tests': failed_tests,
            'validation_details': validation_results
        }

        # 记录验证结果
        if all_passed:
            self.logger.info(f"✓ 被试 {subject_id:02d} 所有验证通过 ({summary['passed_count']}/{summary['total_count']})")
        else:
            self.logger.error(f"✗ 被试 {subject_id:02d} 验证失败 ({summary['passed_count']}/{summary['total_count']})")
            self.logger.error(f"失败项目: {', '.join(failed_tests)}")

            for test_name in failed_tests:
                result = validation_results[test_name]
                if 'issue' in result:
                    self.logger.error(f"  {test_name}: {result['issue']}")

        return summary

    def validate_time_window(self):
        """验证时间窗口"""
        expected_min = self.validation_standards['time_window']['min_time']
        expected_max = self.validation_standards['time_window']['max_time']
        tolerance = self.validation_standards['time_window']['tolerance']

        actual_min = VIS_TMIN * 1000
        actual_max = VIS_TMAX * 1000

        min_ok = abs(actual_min - expected_min) <= tolerance
        max_ok = abs(actual_max - expected_max) <= tolerance

        result = {
            'passed': min_ok and max_ok,
            'expected_range': f"{expected_min}ms to {expected_max}ms",
            'actual_range': f"{actual_min}ms to {actual_max}ms",
            'min_deviation': actual_min - expected_min,
            'max_deviation': actual_max - expected_max,
            'tolerance': tolerance
        }

        if not result['passed']:
            result['issue'] = f"时间窗口不符合要求: 期望{expected_min}到{expected_max}ms，实际{actual_min}到{actual_max}ms"

        return result

    def validate_subplot_aspect_ratio(self):
        """验证子图比例"""
        expected_total_ratio = self.validation_standards['subplot_aspect_ratio']['target_ratio']
        tolerance_ratio = self.validation_standards['subplot_aspect_ratio']['tolerance']

        # 检查figsize设置 - 应该是(24, 4)以确保每个子图2:1
        figsize_ratio = 24 / 4  # 6:1总比例，每个子图2:1
        ideal_total_ratio = 6.0

        ratio_ok = abs(figsize_ratio - ideal_total_ratio) <= tolerance_ratio

        result = {
            'passed': ratio_ok,
            'expected_total_ratio': ideal_total_ratio,
            'actual_figsize_ratio': figsize_ratio,
            'deviation': figsize_ratio - ideal_total_ratio
        }

        if not result['passed']:
            result['issue'] = f"图片总体比例不合适，当前{figsize_ratio:.1f}:1，期望{ideal_total_ratio}:1"

        return result

    def validate_r_peak_alignment(self, epochs):
        """验证R波对齐"""
        times_ms = epochs.times * 1000
        target_position = self.validation_standards['r_peak_alignment']['target_position']
        tolerance = self.validation_standards['r_peak_alignment']['tolerance']

        zero_idx = np.argmin(np.abs(times_ms - target_position))
        actual_zero_time = times_ms[zero_idx]

        alignment_ok = abs(actual_zero_time - target_position) <= tolerance

        result = {
            'passed': alignment_ok,
            'target_position': target_position,
            'actual_zero_position': actual_zero_time,
            'deviation': actual_zero_time - target_position,
            'tolerance': tolerance
        }

        if not result['passed']:
            result['issue'] = f"R波对齐偏差过大: 期望0ms，实际{actual_zero_time:.1f}ms"

        return result

    def validate_hep_quality(self, epochs):
        """验证HEP波形质量 - 严格标准"""
        times_ms = epochs.times * 1000
        epochs_data_uv = epochs.get_data()[:, :EEG_CHANNEL_COUNT, :] * 1e6

        # 检查基线期稳定性
        baseline_mask = (times_ms >= BASELINE_TMIN * 1000) & (times_ms <= BASELINE_TMAX * 1000)
        baseline_data = epochs_data_uv[:, :, baseline_mask]
        baseline_std = np.std(baseline_data, axis=2).mean()

        baseline_ok = baseline_std <= self.validation_standards['hep_quality']['baseline_std_max']

        # 检查HEP成分
        hep_start_time = self.validation_standards['hep_quality']['hep_window_start']
        hep_mask = times_ms >= hep_start_time

        if np.sum(hep_mask) > 0:
            hep_data = epochs_data_uv[:, :, hep_mask]
            hep_std = np.std(hep_data, axis=2).mean()

            min_change_ratio = self.validation_standards['hep_quality']['hep_change_min_ratio']
            hep_change_ok = hep_std >= baseline_std * min_change_ratio
        else:
            hep_change_ok = False
            hep_std = 0

        result = {
            'passed': baseline_ok and hep_change_ok,
            'baseline_std': baseline_std,
            'baseline_threshold': self.validation_standards['hep_quality']['baseline_std_max'],
            'baseline_ok': baseline_ok,
            'hep_std': hep_std,
            'hep_change_ratio': hep_std / baseline_std if baseline_std > 0 else 0,
            'min_change_ratio': min_change_ratio,
            'hep_change_ok': hep_change_ok
        }

        if not baseline_ok:
            result['issue'] = f"基线不稳定: std={baseline_std:.1f}μV > {self.validation_standards['hep_quality']['baseline_std_max']}μV"
        elif not hep_change_ok:
            result['issue'] = f"HEP成分不明显: 变化比例{hep_std/baseline_std:.1f} < {min_change_ratio}"

        return result

    def validate_r_peak_visibility(self, epochs, ecg_signal, r_peaks, sampling_rate):
        """验证R波可见性 - 严格标准"""
        times_ms = epochs.times * 1000
        peak_window = self.validation_standards['r_peak_visibility']['peak_window']

        zero_idx = np.argmin(np.abs(times_ms))
        zero_time = times_ms[zero_idx]

        window_mask = (times_ms >= (zero_time - peak_window)) & (times_ms <= (zero_time + peak_window))

        if np.sum(window_mask) == 0:
            return {
                'passed': False,
                'issue': "无法找到R峰检查窗口"
            }

        # 使用第一个epoch的ECG数据进行检查
        if epochs.get_data().shape[1] >= TOTAL_CHANNELS:
            ecg_channel_idx = -1
            ecg_epoch_data = epochs.get_data()[0, ecg_channel_idx, window_mask]
        else:
            return {
                'passed': True,
                'note': "无法直接验证ECG信号，假设R峰检测正确"
            }

        zero_window_idx = np.argmin(np.abs(times_ms[window_mask] - zero_time))
        max_idx = np.argmax(np.abs(ecg_epoch_data))

        max_position_error = self.validation_standards['r_peak_visibility']['max_position_error']
        is_peak_at_zero = abs(zero_window_idx - max_idx) <= max_position_error

        result = {
            'passed': is_peak_at_zero,
            'zero_position_ms': zero_time,
            'peak_window_ms': peak_window,
            'zero_window_idx': zero_window_idx,
            'max_idx': max_idx,
            'idx_difference': abs(zero_window_idx - max_idx),
            'max_allowed_error': max_position_error
        }

        if not is_peak_at_zero:
            result['issue'] = f"R峰未在0ms位置: 最大值在索引{max_idx}，0ms在索引{zero_window_idx}，偏差{abs(zero_window_idx - max_idx)}个采样点"

        return result

def create_hep_visualization_quality_first(epochs, subject_id, stage_type, stage_number,
                                          ch_names, ecg_channel, r_peak_method, logger=None):
    """创建高质量的HEP波形可视化图片"""
    try:
        # 使用正确的figsize确保每个子图横纵比2:1
        fig, axes = plt.subplots(1, 3, figsize=(24, 4))
        fig.suptitle(f'Subject {subject_id:02d} - {stage_type}_{stage_number} HEP Average\n'
                    f'ECG: {ecg_channel}, Method: {r_peak_method}', fontsize=16)

        times_ms = epochs.times * 1000
        vis_mask = (times_ms >= VIS_TMIN * 1000) & (times_ms <= VIS_TMAX * 1000)
        vis_times = times_ms[vis_mask]
        epochs_data_uv = epochs.get_data() * 1e6

        electrode_groups = [
            (CENTRAL_ELECTRODES, 'Central', 'red'),
            (LEFT_HEMISPHERE, 'Left Hemisphere', 'blue'),
            (RIGHT_HEMISPHERE, 'Right Hemisphere', 'green')
        ]

        for i, (electrodes, title, color) in enumerate(electrode_groups):
            ax = axes[i]

            electrode_indices = []
            for electrode in electrodes:
                if electrode in ch_names:
                    electrode_indices.append(ch_names.index(electrode))

            if electrode_indices:
                group_data = epochs_data_uv[:, electrode_indices, :]
                avg_data = np.mean(group_data, axis=(0, 1))
                vis_avg_data = avg_data[vis_mask]

                ax.plot(vis_times, vis_avg_data, color=color, linewidth=2,
                       label=f'{title} (n={len(electrode_indices)})')

                ax.axhline(y=0, color='black', linestyle='--', alpha=0.5)
                ax.axvline(x=0, color='red', linestyle='--', alpha=0.7, label='R-peak', linewidth=2)
                ax.axvspan(BASELINE_TMIN*1000, BASELINE_TMAX*1000, alpha=0.2, color='gray', label='Baseline')

                ax.set_xlabel('Time (ms)', fontsize=12)
                ax.set_ylabel('Amplitude (μV)', fontsize=12)
                ax.set_title(title, fontsize=14)
                ax.grid(True, alpha=0.3)
                ax.legend(fontsize=10)
                ax.set_xlim(VIS_TMIN * 1000, VIS_TMAX * 1000)

                y_range = np.max(vis_avg_data) - np.min(vis_avg_data)
                y_margin = y_range * 0.1
                ax.set_ylim(np.min(vis_avg_data) - y_margin, np.max(vis_avg_data) + y_margin)
                ax.set_aspect('auto')
            else:
                ax.text(0.5, 0.5, f'No {title} electrodes', ha='center', va='center', transform=ax.transAxes)
                ax.set_title(f'{title} (No Data)')
                ax.set_xlim(VIS_TMIN * 1000, VIS_TMAX * 1000)

        plt.tight_layout()

        filename = f"{subject_id:02d}_{stage_type}_{stage_number}_hep_average.png"
        filepath = os.path.join(PLOTS_DIR, filename)
        plt.savefig(filepath, dpi=100, bbox_inches='tight')
        plt.close()

        if logger:
            logger.info(f"保存高质量HEP可视化图片: {filename}")

        return filepath

    except Exception as e:
        if logger:
            logger.error(f"创建HEP可视化失败: {str(e)}")
        return None

def process_subject_with_quality_improvement(subject_id, stage_number, stage_type,
                                           quality_analyzer, preprocessor, validator, logger):
    """
    数据质量优先的被试处理函数

    核心原则：通过改善数据预处理来解决质量问题，而不是放宽验证标准
    """
    processing_start_time = time.time()

    logger.info(f"开始处理被试 {subject_id:02d} - {stage_type}_{stage_number}")

    # 1. 加载数据
    raw = load_data(subject_id, stage_number, stage_type, logger)
    if raw is None:
        return None

    # 2. 数据质量分析
    quality_report = quality_analyzer.analyze_signal_quality(raw, subject_id)
    quality_analyzer.save_quality_analysis(quality_report)

    # 3. 多层次预处理策略
    preprocessing_levels = [
        ('standard', preprocessor.apply_standard_filtering),
        ('enhanced', preprocessor.apply_enhanced_preprocessing),
        ('strict', preprocessor.apply_strict_preprocessing)
    ]

    for level_name, preprocessing_func in preprocessing_levels:
        try:
            logger.info(f"尝试 {level_name} 预处理级别")

            # 应用预处理
            raw_processed = preprocessing_func(raw)

            # ECG导联选择
            try:
                best_ecg_channel, best_ecg_signal, ecg_quality_report = select_best_ecg_channel(raw_processed, logger)
            except Exception as e:
                logger.warning(f"{level_name} 预处理 - ECG导联选择失败: {str(e)}")
                continue

            # R峰检测
            try:
                r_peaks, r_peak_method, r_peak_quality = detect_r_peaks_optimized(
                    best_ecg_signal, raw_processed.info['sfreq'], logger)
            except Exception as e:
                logger.warning(f"{level_name} 预处理 - R峰检测失败: {str(e)}")
                continue

            # 提取HEP epochs
            epochs = extract_hep_epochs(raw_processed, r_peaks, logger)
            if epochs is None:
                logger.warning(f"{level_name} 预处理 - HEP epochs提取失败")
                continue

            # 创建可视化
            image_path = create_hep_visualization_quality_first(epochs, subject_id, stage_type, stage_number,
                                                              epochs.ch_names, best_ecg_channel, r_peak_method, logger)
            if image_path is None:
                logger.warning(f"{level_name} 预处理 - 可视化创建失败")
                continue

            # 严格验证
            validation_summary = validator.validate_all_criteria(
                subject_id, stage_type, stage_number, image_path, epochs,
                best_ecg_signal, r_peaks, raw_processed.info['sfreq'])

            # 检查验证结果
            if validation_summary['all_passed']:
                # 验证通过，组织数据并返回
                epochs_data = epochs.get_data()
                ch_names = epochs.ch_names
                organized_data = organize_electrode_data(epochs_data, ch_names, logger)

                processing_time = time.time() - processing_start_time
                complete_quality_report = {
                    'subject_id': subject_id,
                    'stage_type': stage_type,
                    'stage_number': stage_number,
                    'processing_time': processing_time,
                    'preprocessing_level': level_name,
                    'ecg_channel_selection': {
                        'selected_channel': best_ecg_channel,
                        'quality_scores': ecg_quality_report
                    },
                    'r_peak_detection': r_peak_quality,
                    'final_epochs_count': len(epochs),
                    'validation_summary': validation_summary,
                    'quality_analysis': quality_report,
                    'processing_success': True
                }

                logger.info(f"✓ 被试 {subject_id:02d} 处理成功 ({level_name} 预处理) "
                           f"({len(epochs)} epochs, {processing_time:.1f}s, ECG: {best_ecg_channel})")

                return {
                    'organized_data': organized_data,
                    'quality_report': complete_quality_report
                }
            else:
                # 验证失败，记录详细信息并尝试下一级预处理
                failed_tests = validation_summary['failed_tests']
                logger.warning(f"被试 {subject_id:02d} {level_name} 预处理验证失败: {', '.join(failed_tests)}")

                # 保存验证报告
                validation_filename = f"{subject_id:02d}_{stage_type}_{stage_number}_{level_name}_validation_report.json"
                validation_filepath = os.path.join(VALIDATION_DIR, validation_filename)
                with open(validation_filepath, 'w', encoding='utf-8') as f:
                    json.dump(validation_summary, f, indent=2, ensure_ascii=False, default=str)

                # 如果是最后一级预处理，记录为最终失败
                if level_name == 'strict':
                    logger.error(f"✗ 被试 {subject_id:02d} 所有预处理级别都无法通过验证")

                    # 生成数据质量问题报告
                    quality_issue_report = {
                        'subject_id': subject_id,
                        'stage_type': stage_type,
                        'stage_number': stage_number,
                        'issue_type': 'validation_failure_all_levels',
                        'failed_tests': failed_tests,
                        'preprocessing_attempts': ['standard', 'enhanced', 'strict'],
                        'final_validation_details': validation_summary['validation_details'],
                        'quality_analysis': quality_report,
                        'recommendation': '需要手动检查原始数据质量'
                    }

                    issue_filename = f"{subject_id:02d}_{stage_type}_{stage_number}_quality_issue.json"
                    issue_filepath = os.path.join(QUALITY_ANALYSIS_DIR, issue_filename)
                    with open(issue_filepath, 'w', encoding='utf-8') as f:
                        json.dump(quality_issue_report, f, indent=2, ensure_ascii=False, default=str)

                    return None
                else:
                    logger.info(f"尝试下一级预处理...")
                    continue

        except Exception as e:
            logger.error(f"被试 {subject_id:02d} {level_name} 预处理异常: {str(e)}")
            continue

    # 如果所有预处理级别都失败
    logger.error(f"✗ 被试 {subject_id:02d} 所有预处理级别都失败")
    return None

def save_hep_to_hdf5(stage_data, stage_type, stage_number, output_dir, logger=None):
    """将HEP数据保存为HDF5格式"""
    try:
        filename = f"{stage_type}_{stage_number}_hep_data.h5"
        filepath = os.path.join(output_dir, filename)

        with h5py.File(filepath, 'w') as f:
            for data_type, data_list in stage_data.items():
                if data_list:
                    combined_data = np.concatenate(data_list, axis=0)
                    f.create_dataset(data_type, data=combined_data, compression='gzip')
                    if logger:
                        logger.info(f"保存 {data_type}: {combined_data.shape}")

        if logger:
            logger.info(f"成功保存HDF5文件: {filename}")
        return True

    except Exception as e:
        if logger:
            logger.error(f"保存HDF5文件失败: {str(e)}")
        return False

def main(background_mode=False):
    """主函数 - 数据质量优先版本"""
    # 设置日志
    logger = setup_logging(background_mode)

    logger.info("开始HEP数据提取 - 数据质量优先版本")
    logger.info("核心原则：通过改善数据预处理来解决质量问题，而不是放宽验证标准")

    logger.info(f"数据源目录: {DATA_DIR}")
    logger.info(f"输出目录: {OUTPUT_DIR}")
    logger.info(f"图片目录: {PLOTS_DIR}")
    logger.info(f"验证报告目录: {VALIDATION_DIR}")
    logger.info(f"质量分析目录: {QUALITY_ANALYSIS_DIR}")

    # 严格验证标准信息
    logger.info(f"\n=== 严格验证标准（不放宽） ===")
    logger.info(f"1. 可视化时间窗口: {VIS_TMIN*1000}ms 到 {VIS_TMAX*1000}ms")
    logger.info(f"2. 子图横纵比: 2:1")
    logger.info(f"3. R波对齐: 精确到0ms位置")
    logger.info(f"4. HEP波形质量: 基线稳定(<30μV)，200ms后有明显成分(>2.0倍基线)")
    logger.info(f"5. R波可见性: 0ms位置为最大幅值(±5个采样点)")

    # 创建处理组件
    quality_analyzer = DataQualityAnalyzer(logger)
    preprocessor = MultiLevelPreprocessor(logger)
    validator = StrictQualityValidator(logger)

    # 统计信息
    total_files = len(SUBJECTS) * len(STAGE_NUMBERS) * len(STAGES)
    processed_files = 0
    failed_files = 0
    quality_issues = 0

    logger.info(f"计划处理 {total_files} 个文件")
    logger.info(f"被试数量: {len(SUBJECTS)}")
    logger.info(f"阶段数量: {len(STAGES)} × {len(STAGE_NUMBERS)} = {len(STAGES) * len(STAGE_NUMBERS)}")

    # 特别关注30号被试
    if 30 in SUBJECTS:
        logger.info(f"\n=== 特别关注30号被试 ===")
        logger.info(f"将对30号被试进行深度数据质量分析")

    # 按阶段处理数据
    for stage_type in STAGES:
        for stage_number in STAGE_NUMBERS:
            logger.info(f"\n{'='*60}")
            logger.info(f"开始处理阶段: {stage_type}_{stage_number}")
            logger.info(f"{'='*60}")

            # 初始化该阶段的数据存储
            stage_data = {
                'all_channels_hep': [],
                'central_electrodes_hep': [],
                'right_hemisphere_hep': [],
                'left_hemisphere_hep': []
            }

            processed_subjects = []

            # 逐被试处理
            for subject_id in SUBJECTS:
                logger.info(f"\n--- 处理被试 {subject_id:02d} ---")

                result = process_subject_with_quality_improvement(
                    subject_id, stage_number, stage_type,
                    quality_analyzer, preprocessor, validator, logger)

                if result:
                    # 处理成功
                    organized_data = result['organized_data']
                    quality_report = result['quality_report']

                    for data_type, data in organized_data.items():
                        if data_type in stage_data:
                            stage_data[data_type].append(data)

                    processed_subjects.append(subject_id)
                    processed_files += 1

                    logger.info(f"✓ 被试 {subject_id:02d} 完成并通过严格验证")
                else:
                    failed_files += 1
                    quality_issues += 1
                    logger.error(f"✗ 被试 {subject_id:02d} 数据质量问题，无法通过严格验证")

            # 保存该阶段的最终数据
            if any(stage_data.values()):
                save_hep_to_hdf5(stage_data, stage_type, stage_number, OUTPUT_DIR, logger)
                logger.info(f"✓ 阶段 {stage_type}_{stage_number} 完成: {len(processed_subjects)} 个被试成功处理")
            else:
                logger.warning(f"⚠ 阶段 {stage_type}_{stage_number} 没有有效数据")

    # 最终报告
    logger.info(f"\n{'='*60}")
    logger.info(f"数据质量优先处理完成!")
    logger.info(f"{'='*60}")
    logger.info(f"总文件数: {total_files}")
    logger.info(f"成功处理: {processed_files}")
    logger.info(f"数据质量问题: {quality_issues}")
    logger.info(f"处理失败: {failed_files}")
    logger.info(f"成功率: {processed_files/total_files*100:.1f}%")
    logger.info(f"数据质量合格率: {processed_files/(processed_files+quality_issues)*100:.1f}%")

    # 生成最终质量总结报告
    final_summary = {
        'processing_date': time.strftime('%Y-%m-%d %H:%M:%S'),
        'processing_mode': 'quality_first',
        'validation_standards': 'strict_no_relaxation',
        'total_files': total_files,
        'processed_files': processed_files,
        'quality_issues': quality_issues,
        'failed_files': failed_files,
        'success_rate': processed_files / total_files * 100,
        'quality_pass_rate': processed_files / (processed_files + quality_issues) * 100 if (processed_files + quality_issues) > 0 else 0,
        'strict_validation_standards': STRICT_VALIDATION_STANDARDS,
        'preprocessing_levels': ['standard', 'enhanced', 'strict'],
        'principle': '数据质量问题通过改善预处理解决，不放宽验证标准'
    }

    summary_path = os.path.join(QUALITY_ANALYSIS_DIR, 'final_quality_summary.json')
    with open(summary_path, 'w', encoding='utf-8') as f:
        json.dump(final_summary, f, indent=2, ensure_ascii=False, default=str)

    logger.info(f"最终质量总结报告: {summary_path}")

    if quality_issues == 0:
        logger.info("🎉 所有文件都通过了严格验证！数据质量优秀！")
    else:
        logger.warning(f"⚠️ {quality_issues} 个文件存在数据质量问题，需要进一步分析")
        logger.info(f"质量问题报告位置: {QUALITY_ANALYSIS_DIR}")

if __name__ == "__main__":
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='HEP数据提取脚本 - 数据质量优先版本')
    parser.add_argument('--background', action='store_true', help='后台运行模式')
    args = parser.parse_args()

    main(background_mode=args.background)