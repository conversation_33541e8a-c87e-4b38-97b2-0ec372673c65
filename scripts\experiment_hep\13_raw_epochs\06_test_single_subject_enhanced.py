#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试单个被试的增强HEP提取
验证新的滤波参数和基线校正方法的效果

作者: HEP分析团队
日期: 2024年
"""

import os
import sys
import numpy as np
import mne
import neurokit2 as nk
import h5py
import logging
import time
import warnings
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 忽略警告
warnings.filterwarnings('ignore')
mne.set_log_level('ERROR')

# 基础路径配置
BASE_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
DATA_DIR = r"D:\ecgeeg\19-eegecg手动预处理6-ICA3"
OUTPUT_DIR = os.path.join(BASE_DIR, 'result', 'hep_analysis', '13_raw_epochs', '06_test_single_enhanced')
os.makedirs(OUTPUT_DIR, exist_ok=True)

# 数据结构配置
TOTAL_CHANNELS = 119
EEG_CHANNELS = 61
ECG_CHANNELS = 58
SAMPLING_RATE = 500

# 增强HEP提取参数
HEP_TMIN = -0.5
HEP_TMAX = 1.5
DISPLAY_TMIN = -0.2
DISPLAY_TMAX = 0.65
BASELINE_TMIN = -0.15
BASELINE_TMAX = -0.05
FILTER_LOW = 0.5
FILTER_HIGH = 30.0
REFERENCE_ECG = 'ECG11'

# 电极组合
CENTRAL_ELECTRODES = ['F1', 'F2', 'FC1', 'FC2', 'C1', 'Cz', 'C2', 'Fz']

def setup_logging():
    """设置日志系统"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler(sys.stdout)]
    )
    return logging.getLogger(__name__)

def load_data(subject_id=1, stage_number='01', stage_type='prac', logger=None):
    """加载数据文件"""
    try:
        filename = f"{subject_id:02d}_{stage_number}_reto2_combined_baseline_corrected_chanloc_bad_interp_avgref_ica_{stage_type}.fif"
        file_path = os.path.join(DATA_DIR, filename)
        
        if not os.path.exists(file_path):
            logger.error(f"文件不存在: {filename}")
            return None, None
        
        raw = mne.io.read_raw_fif(file_path, preload=True, verbose=False)
        logger.info(f"成功加载数据: {filename}")
        logger.info(f"数据形状: {raw.get_data().shape}")
        logger.info(f"采样率: {raw.info['sfreq']}Hz")
        logger.info(f"通道数: {len(raw.ch_names)}")
        
        return raw, filename
        
    except Exception as e:
        logger.error(f"加载数据失败: {str(e)}")
        return None, None

def convert_voltage_units(raw, logger):
    """检测和转换电压单位"""
    try:
        eeg_data = raw.get_data()[:EEG_CHANNELS, :1000]
        eeg_std = np.std(eeg_data)
        
        logger.info(f"脑电原始数据标准差: {eeg_std:.2e}")
        
        if eeg_std > 1e-3:
            factor = 1e6
            unit = "V"
        elif eeg_std > 1e-6:
            factor = 1e3
            unit = "mV"
        else:
            factor = 1.0
            unit = "μV"
        
        if factor != 1.0:
            raw._data[:EEG_CHANNELS, :] *= factor
            logger.info(f"脑电数据从{unit}转换为μV，转换系数: {factor}")
        
        # 验证转换后的数据
        converted_std = np.std(raw.get_data()[:EEG_CHANNELS, :])
        logger.info(f"转换后脑电标准差: {converted_std:.2f}μV")
        
        return raw
        
    except Exception as e:
        logger.error(f"电压单位转换失败: {str(e)}")
        return raw

def apply_enhanced_filtering(raw, logger):
    """应用增强滤波"""
    try:
        raw_filtered = raw.copy()
        raw_filtered.filter(l_freq=FILTER_LOW, h_freq=FILTER_HIGH, fir_design='firwin', verbose=False)
        logger.info(f"应用增强滤波: {FILTER_LOW}-{FILTER_HIGH}Hz")
        return raw_filtered
    except Exception as e:
        logger.error(f"滤波失败: {str(e)}")
        return raw

def extract_ecg_and_detect_r_peaks(raw, logger):
    """提取心电信号并检测R波"""
    try:
        # 提取ECG11通道
        ecg_idx = raw.ch_names.index(REFERENCE_ECG)
        ecg_signal = raw.get_data()[ecg_idx, :]
        
        logger.info(f"提取心电通道: {REFERENCE_ECG}")
        logger.info(f"心电信号标准差: {np.std(ecg_signal):.2f}μV")
        
        # 清洗ECG信号
        ecg_cleaned = nk.ecg_clean(ecg_signal, sampling_rate=SAMPLING_RATE)
        
        # 检测R峰
        _, rpeaks_info = nk.ecg_peaks(ecg_cleaned, sampling_rate=SAMPLING_RATE, method='neurokit')
        r_peaks = rpeaks_info.get('ECG_R_Peaks', [])
        
        logger.info(f"检测到 {len(r_peaks)} 个R峰")
        
        if len(r_peaks) < 30:
            logger.warning(f"R峰数量过少: {len(r_peaks)}")
        
        return r_peaks, ecg_cleaned
        
    except Exception as e:
        logger.error(f"R峰检测失败: {str(e)}")
        return None, None

def extract_hep_epochs(raw, r_peaks, logger):
    """提取HEP epochs"""
    try:
        # 创建事件数组
        events = np.column_stack([r_peaks, np.zeros_like(r_peaks), np.ones_like(r_peaks)])
        
        # 创建epochs
        epochs = mne.Epochs(raw, events, event_id=1,
                           tmin=HEP_TMIN, tmax=HEP_TMAX,
                           baseline=None, preload=True, reject=None, verbose=False)
        
        # 手动基线校正
        baseline_mask = (epochs.times >= BASELINE_TMIN) & (epochs.times <= BASELINE_TMAX)
        data = epochs.get_data()
        baseline_data = data[:, :, baseline_mask]
        baseline_mean = np.mean(baseline_data, axis=2, keepdims=True)
        data_corrected = data - baseline_mean
        epochs._data = data_corrected
        
        logger.info(f"创建了 {len(epochs)} 个HEP epochs")
        logger.info(f"基线校正窗口: {BASELINE_TMIN*1000:.0f}ms 到 {BASELINE_TMAX*1000:.0f}ms")
        
        # 验证基线校正效果
        baseline_after = np.mean(data_corrected[:, :EEG_CHANNELS, baseline_mask])
        logger.info(f"基线校正后平均值: {baseline_after:.6f} μV")
        
        return epochs
        
    except Exception as e:
        logger.error(f"HEP epochs提取失败: {str(e)}")
        return None

def create_hep_visualization(epochs, subject_id, stage_type, stage_number, logger):
    """创建HEP可视化"""
    try:
        # 获取EEG数据
        eeg_data = epochs.get_data()[:, :EEG_CHANNELS, :]
        mean_hep = np.mean(eeg_data, axis=0)
        
        # 找到中央电极索引
        central_indices = []
        for electrode in CENTRAL_ELECTRODES:
            if electrode in epochs.ch_names[:EEG_CHANNELS]:
                central_indices.append(epochs.ch_names.index(electrode))
        
        if not central_indices:
            logger.warning("未找到中央电极")
            return None
        
        # 计算中央电极平均波形
        central_data = mean_hep[central_indices, :]
        central_mean = np.mean(central_data, axis=0)
        central_std = np.std(central_data, axis=0)
        
        # 创建可视化窗口
        display_mask = (epochs.times >= DISPLAY_TMIN) & (epochs.times <= DISPLAY_TMAX)
        display_times = epochs.times[display_mask] * 1000  # 转换为ms
        display_signal = central_mean[display_mask]
        display_std = central_std[display_mask]
        
        # 创建图表
        fig, ax = plt.subplots(figsize=(12, 6))
        
        # 绘制平均波形和标准差
        ax.plot(display_times, display_signal, 'b-', linewidth=2, label=f'中央电极平均 (n={len(central_indices)})')
        ax.fill_between(display_times, display_signal - display_std, display_signal + display_std, 
                       alpha=0.3, color='blue')
        
        # 标记重要时间点
        ax.axvline(x=0, color='red', linestyle='--', alpha=0.7, label='R波')
        ax.axvspan(200, 600, alpha=0.1, color='yellow', label='HEP窗口')
        ax.axvspan(BASELINE_TMIN*1000, BASELINE_TMAX*1000, alpha=0.1, color='green', label='基线校正窗口')
        
        # 设置坐标轴
        ax.set_xlabel('时间 (ms)')
        ax.set_ylabel('幅度 (μV)')
        ax.set_title(f'被试{subject_id:02d} - {stage_type}_{stage_number} 增强HEP波形\n'
                    f'滤波: {FILTER_LOW}-{FILTER_HIGH}Hz, 基线: {BASELINE_TMIN*1000:.0f}到{BASELINE_TMAX*1000:.0f}ms')
        ax.grid(True, alpha=0.3)
        ax.legend()
        
        # 自动调整Y轴范围
        y_range = max(np.abs(display_signal)) * 1.2
        ax.set_ylim(-y_range, y_range)
        
        plt.tight_layout()
        
        # 保存图表
        output_filename = f"subject_{subject_id:02d}_{stage_type}_{stage_number}_enhanced_hep.png"
        output_path = os.path.join(OUTPUT_DIR, output_filename)
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"HEP可视化已保存: {output_path}")
        
        # 输出信号统计信息
        logger.info(f"HEP信号统计:")
        logger.info(f"  - 信号幅度范围: {np.min(display_signal):.3f} 到 {np.max(display_signal):.3f} μV")
        logger.info(f"  - 信号标准差: {np.std(display_signal):.3f} μV")
        logger.info(f"  - 峰峰值: {np.max(display_signal) - np.min(display_signal):.3f} μV")
        
        return output_path
        
    except Exception as e:
        logger.error(f"创建HEP可视化失败: {str(e)}")
        return None

def main():
    """主函数"""
    logger = setup_logging()
    logger.info("开始增强HEP提取测试")
    
    # 测试被试01的练习阶段
    subject_id = 1
    stage_number = '01'
    stage_type = 'prac'
    
    logger.info(f"测试被试: {subject_id:02d}")
    logger.info(f"阶段: {stage_type}_{stage_number}")
    logger.info(f"增强参数: 滤波 {FILTER_LOW}-{FILTER_HIGH}Hz, 基线 {BASELINE_TMIN*1000:.0f}到{BASELINE_TMAX*1000:.0f}ms")
    
    # 1. 加载数据
    raw, filename = load_data(subject_id, stage_number, stage_type, logger)
    if raw is None:
        logger.error("数据加载失败，退出")
        return
    
    # 2. 转换电压单位
    raw = convert_voltage_units(raw, logger)
    
    # 3. 应用增强滤波
    raw_filtered = apply_enhanced_filtering(raw, logger)
    
    # 4. 提取心电信号并检测R波
    r_peaks, ecg_cleaned = extract_ecg_and_detect_r_peaks(raw_filtered, logger)
    if r_peaks is None:
        logger.error("R波检测失败，退出")
        return
    
    # 5. 提取HEP epochs
    epochs = extract_hep_epochs(raw_filtered, r_peaks, logger)
    if epochs is None:
        logger.error("HEP epochs提取失败，退出")
        return
    
    # 6. 创建可视化
    viz_path = create_hep_visualization(epochs, subject_id, stage_type, stage_number, logger)
    
    logger.info("=== 增强HEP提取测试完成 ===")
    logger.info(f"成功提取 {len(epochs)} 个HEP epochs")
    logger.info(f"检测到 {len(r_peaks)} 个R峰")
    if viz_path:
        logger.info(f"可视化文件: {viz_path}")

if __name__ == "__main__":
    main()
