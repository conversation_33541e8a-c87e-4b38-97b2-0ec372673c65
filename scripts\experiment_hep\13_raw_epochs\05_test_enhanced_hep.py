#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强HEP提取效果
对比标准方法和增强方法的HEP信号质量

主要功能：
1. 测试单个被试的HEP提取
2. 生成对比可视化
3. 验证信号改进效果

作者: HEP分析团队
日期: 2024年
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))

# 导入增强HEP提取模块
try:
    from scripts.experiment_hep.raw_epochs.enhanced_hep_extraction import (
        setup_logging, process_single_subject_enhanced,
        CENTRAL_ELECTRODES, LEFT_HEMISPHERE, RIGHT_HEMISPHERE
    )
except ImportError:
    # 直接导入模块
    import importlib.util
    spec = importlib.util.spec_from_file_location(
        "enhanced_hep_extraction",
        os.path.join(os.path.dirname(__file__), "04_enhanced_hep_extraction.py")
    )
    enhanced_hep = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(enhanced_hep)

    setup_logging = enhanced_hep.setup_logging
    process_single_subject_enhanced = enhanced_hep.process_single_subject_enhanced
    CENTRAL_ELECTRODES = enhanced_hep.CENTRAL_ELECTRODES
    LEFT_HEMISPHERE = enhanced_hep.LEFT_HEMISPHERE
    RIGHT_HEMISPHERE = enhanced_hep.RIGHT_HEMISPHERE

# 基础路径配置
BASE_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
OUTPUT_DIR = os.path.join(BASE_DIR, 'result', 'hep_analysis', '13_raw_epochs', '05_test_enhanced')
os.makedirs(OUTPUT_DIR, exist_ok=True)

def test_single_subject_hep(subject_id=1, stage_type='prac', stage_number='01'):
    """测试单个被试的增强HEP提取"""
    logger = setup_logging()

    logger.info(f"开始测试被试 {subject_id:02d} - {stage_type}_{stage_number}")

    # 处理被试数据
    result = process_single_subject_enhanced(subject_id, stage_type, stage_number, logger)

    if result and result.get('processing_success', False):
        logger.info("增强HEP提取测试成功！")
        logger.info(f"提取了 {result['n_epochs']} 个HEP epochs")
        logger.info(f"检测了 {result['n_r_peaks']} 个R峰")

        if result.get('validation_results'):
            validation = result['validation_results']
            logger.info("质量验证结果:")
            for key, value in validation.items():
                if isinstance(value, dict) and 'passed' in value:
                    status = "通过" if value['passed'] else "未通过"
                    logger.info(f"  - {key}: {status}")

        return result
    else:
        logger.error("增强HEP提取测试失败！")
        if result:
            logger.error(f"错误: {result.get('error', 'Unknown')}")
        return None

def create_hep_visualization(hep_data, times, ch_names, subject_id, stage_type, stage_number,
                           validation_results=None):
    """创建HEP可视化图表"""
    try:
        # 找到EEG通道索引
        eeg_channels = 61  # 前61个通道是EEG
        eeg_data = hep_data[:, :eeg_channels, :]

        # 计算平均HEP波形
        mean_hep = np.mean(eeg_data, axis=0)

        # 创建图表
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        fig.suptitle(f'被试{subject_id:02d} - {stage_type}_{stage_number} 增强HEP波形',
                    fontsize=16, fontweight='bold')

        # 定义电极组
        electrode_groups = {
            '中央电极': CENTRAL_ELECTRODES,
            '左半球电极': LEFT_HEMISPHERE,
            '右半球电极': RIGHT_HEMISPHERE
        }

        colors = ['blue', 'red', 'green']

        for idx, (group_name, electrodes) in enumerate(electrode_groups.items()):
            ax = axes[idx]

            # 找到电极索引
            electrode_indices = []
            for electrode in electrodes:
                if electrode in ch_names[:eeg_channels]:
                    electrode_indices.append(ch_names.index(electrode))

            if electrode_indices:
                # 计算该组电极的平均波形
                group_data = mean_hep[electrode_indices, :]
                group_mean = np.mean(group_data, axis=0)
                group_std = np.std(group_data, axis=0)

                # 绘制平均波形和标准差
                ax.plot(times * 1000, group_mean, color=colors[idx], linewidth=2,
                       label=f'{group_name} (n={len(electrode_indices)})')
                ax.fill_between(times * 1000, group_mean - group_std, group_mean + group_std,
                               color=colors[idx], alpha=0.3)

                # 标记R波位置
                ax.axvline(x=0, color='black', linestyle='--', alpha=0.7, label='R波')

                # 标记HEP窗口
                ax.axvspan(200, 600, alpha=0.1, color='yellow', label='HEP窗口')

                # 设置坐标轴
                ax.set_xlabel('时间 (ms)')
                ax.set_ylabel('幅度 (μV)')
                ax.set_title(group_name)
                ax.grid(True, alpha=0.3)
                ax.legend()

                # 设置Y轴范围以突出HEP特征
                y_range = np.max(np.abs(group_mean)) * 1.2
                ax.set_ylim(-y_range, y_range)

        plt.tight_layout()

        # 保存图表
        output_filename = f"subject_{subject_id:02d}_{stage_type}_{stage_number}_enhanced_hep_visualization.png"
        output_path = os.path.join(OUTPUT_DIR, output_filename)
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"HEP可视化已保存到: {output_path}")

        # 创建质量报告图表
        if validation_results:
            create_quality_report_chart(validation_results, subject_id, stage_type, stage_number)

        return output_path

    except Exception as e:
        print(f"创建HEP可视化失败: {str(e)}")
        return None

def create_quality_report_chart(validation_results, subject_id, stage_type, stage_number):
    """创建质量验证报告图表"""
    try:
        # 提取质量指标
        metrics = {}
        for key, value in validation_results.items():
            if isinstance(value, dict) and 'passed' in value:
                metrics[key] = value['passed']

        if not metrics:
            return

        # 创建图表
        fig, ax = plt.subplots(figsize=(10, 6))

        metric_names = list(metrics.keys())
        metric_values = [1 if metrics[name] else 0 for name in metric_names]
        colors = ['green' if val else 'red' for val in metric_values]

        bars = ax.bar(range(len(metric_names)), metric_values, color=colors, alpha=0.7)

        # 设置标签
        ax.set_xlabel('质量指标')
        ax.set_ylabel('通过状态')
        ax.set_title(f'被试{subject_id:02d} - {stage_type}_{stage_number} HEP质量验证报告')
        ax.set_xticks(range(len(metric_names)))
        ax.set_xticklabels(metric_names, rotation=45, ha='right')
        ax.set_ylim(0, 1.2)
        ax.set_yticks([0, 1])
        ax.set_yticklabels(['未通过', '通过'])

        # 添加数值标签
        for i, (bar, passed) in enumerate(zip(bars, metric_values)):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.05,
                   '通过' if passed else '未通过',
                   ha='center', va='bottom', fontweight='bold')

        plt.tight_layout()

        # 保存图表
        output_filename = f"subject_{subject_id:02d}_{stage_type}_{stage_number}_quality_report.png"
        output_path = os.path.join(OUTPUT_DIR, output_filename)
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"质量报告已保存到: {output_path}")
        return output_path

    except Exception as e:
        print(f"创建质量报告失败: {str(e)}")
        return None

def main():
    """主测试函数"""
    print("开始增强HEP提取测试...")

    # 测试被试1的练习阶段
    result = test_single_subject_hep(subject_id=1, stage_type='prac', stage_number='01')

    if result and result.get('processing_success', False):
        print("\n=== 测试成功 ===")
        print(f"输出文件: {result['output_path']}")

        # 加载数据进行可视化
        try:
            import h5py
            with h5py.File(result['output_path'], 'r') as f:
                hep_data = f['hep_data'][:]
                times = f['times'][:]
                ch_names = [ch.decode('utf-8') for ch in f['ch_names'][:]]

            # 创建可视化
            viz_path = create_hep_visualization(
                hep_data, times, ch_names,
                result['subject_id'], result['stage_type'], result['stage_number'],
                result.get('validation_results')
            )

            if viz_path:
                print(f"可视化文件: {viz_path}")

        except Exception as e:
            print(f"可视化创建失败: {str(e)}")
    else:
        print("\n=== 测试失败 ===")
        if result:
            print(f"错误: {result.get('error', 'Unknown')}")

if __name__ == "__main__":
    main()
